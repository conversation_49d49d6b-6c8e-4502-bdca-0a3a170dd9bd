<?php
// PHP 5.6 compatible logging script without external extensions
$log_file = '/var/www/vhosts/cdn.camosun.ca/.requests.log';
$ip = $_SERVER['REMOTE_ADDR'];
$date = date('Y-m-d H:i:s');
$request_uri = $_SERVER['REQUEST_URI'];
$referrer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'N/A';
$user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'N/A';

// Format the log entry
$log_entry = sprintf(
    "[%s] IP: %s | URI: %s | Referrer: %s | User-Agent: %s\n",
    $date,
    $ip,
    $request_uri,
    $referrer,
    $user_agent
);

// Append the log entry to the file
file_put_contents($log_file, $log_entry, FILE_APPEND);

// Get the original requested URI
$original_uri = ltrim($_SERVER['REQUEST_URI'], '/');
$original_file = getenv('DOCUMENT_ROOT') . '/' . $original_uri;

// Check if the file exists and serve it
if (file_exists($original_file) && is_file($original_file)) {
    // Determine the content type based on file extension
    $extension = strtolower(pathinfo($original_file, PATHINFO_EXTENSION));
    $mime_types = array(
        'txt' => 'text/plain',
        'html' => 'text/html',
        'css' => 'text/css',
        'js' => 'application/javascript',
        'json' => 'application/json',
        'xml' => 'application/xml',
        'swf' => 'application/x-shockwave-flash',
        'flv' => 'video/x-flv',

        // images
        'png' => 'image/png',
        'jpe' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'jpg' => 'image/jpeg',
        'gif' => 'image/gif',
        'bmp' => 'image/bmp',
        'ico' => 'image/vnd.microsoft.icon',
        'tiff' => 'image/tiff',
        'tif' => 'image/tiff',
        'svg' => 'image/svg+xml',
        'svgz' => 'image/svg+xml',

        // archives
        'zip' => 'application/zip',
        'rar' => 'application/x-rar-compressed',
        'exe' => 'application/x-msdownload',
        'msi' => 'application/x-msdownload',
        'cab' => 'application/vnd.ms-cab-compressed',

        // audio/video
        'mp3' => 'audio/mpeg',
        'qt' => 'video/quicktime',
        'mov' => 'video/quicktime',

        // adobe
        'pdf' => 'application/pdf',
        'psd' => 'image/vnd.adobe.photoshop',
        'ai' => 'application/postscript',
        'eps' => 'application/postscript',
        'ps' => 'application/postscript',

        // ms office
        'doc' => 'application/msword',
        'rtf' => 'application/rtf',
        'xls' => 'application/vnd.ms-excel',
        'ppt' => 'application/vnd.ms-powerpoint',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',

        // open office
        'odt' => 'application/vnd.oasis.opendocument.text',
        'ods' => 'application/vnd.oasis.opendocument.spreadsheet',
    );

    $mime_type = isset($mime_types[$extension]) ? $mime_types[$extension] : 'application/octet-stream';

    header('Content-Type: ' . $mime_type);
    readfile($original_file);
    exit;
} else {
    // Handle the case where the file is not found
    http_response_code(404);
    echo "404 Not Found";
    exit;
}