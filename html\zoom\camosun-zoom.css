	@media screen and (max-width: 768px) {
		h1 {
			font-size: 27px;
		}

		.row {
			margin-bottom: 15px;
		}

		.row>div {
			display: block;
		}

		.footer {
			padding-bottom: 15px;
			height: 40px;
		}

		.footer a {
			display: block;
			width: 100%;
			height: inherit;
			padding-left: 0;
			padding-right: 0;
		}

		.content {
			margin: auto;
		}

		.content>div,
		.made-with {
			text-align: center;
		}
		.content>div {
			text-align: left;
		}

	}

	@media print {
		* {
			background-color: transparent
		}

		#footer,
		#header-region,
		#navigation,
		#search-theme-form,
		.breadcrumb,
		.email,
		.feed-icon,
		.links,
		.sidebar,
		.sitewide_alert__content,
		.sitewide_alert__header,
		.tabs {
			display: none
		}

		#page {
			width: 100%
		}

		#content,
		#content #content-inner,
		.content,
		.title {
			width: auto;
			margin: 2rem 0
		}

		img {
			page-break-before: auto;
			page-break-after: auto;
			page-break-inside: avoid
		}

		a:active,
		a:hover,
		a:link,
		a:visited {
			color: #000
		}

		#content a:link:after,
		#content a:visited:after {
			font-size: .8em;
			font-weight: 400;
			content: " ("attr(href) ") "
		}

		.page-node-type-event .has-sidebar {
			width: 114rem !important
		}
	}

	html {
		line-height: 1.15;
		-webkit-text-size-adjust: 100%
	}

	main {
		display: block
	}

	h1 {
		font-size: 2em;
		margin: .67em 0
	}

	hr {
		-webkit-box-sizing: content-box;
		box-sizing: content-box;
		height: 0;
		overflow: visible
	}

	pre {
		font-family: monospace, monospace;
		font-size: 1em
	}

	a {
		background-color: transparent
	}

	abbr[title] {
		border-bottom: none;
		text-decoration: underline;
		-webkit-text-decoration: underline dotted;
		text-decoration: underline dotted
	}

	b,
	strong {
		font-weight: bolder
	}

	code,
	kbd,
	samp {
		font-family: monospace, monospace;
		font-size: 1em
	}

	small {
		font-size: 80%
	}

	sub,
	sup {
		font-size: 75%;
		line-height: 0;
		position: relative;
		vertical-align: baseline
	}

	sub {
		bottom: -.25em
	}

	sup {
		top: -.5em
	}

	img {
		border-style: none
	}

	button,
	input,
	optgroup,
	select,
	textarea {
		font-family: inherit;
		font-size: 100%;
		line-height: 1.15;
		margin: 0
	}

	button,
	input {
		overflow: visible
	}

	button,
	select {
		text-transform: none
	}

	[type=button],
	[type=reset],
	[type=submit],
	button {
		-webkit-appearance: button
	}

	[type=button]::-moz-focus-inner,
	[type=reset]::-moz-focus-inner,
	[type=submit]::-moz-focus-inner,
	button::-moz-focus-inner {
		border-style: none;
		padding: 0
	}

	[type=button]:-moz-focusring,
	[type=reset]:-moz-focusring,
	[type=submit]:-moz-focusring,
	button:-moz-focusring {
		outline: 1px dotted ButtonText
	}

	fieldset {
		padding: .35em .75em .625em
	}

	legend {
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		color: inherit;
		display: table;
		max-width: 100%;
		padding: 0;
		white-space: normal
	}

	progress {
		vertical-align: baseline
	}

	textarea {
		overflow: auto
	}

	[type=checkbox],
	[type=radio] {
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		padding: 0
	}

	[type=number]::-webkit-inner-spin-button,
	[type=number]::-webkit-outer-spin-button {
		height: auto
	}

	[type=search] {
		-webkit-appearance: textfield;
		outline-offset: -2px
	}

	[type=search]::-webkit-search-decoration {
		-webkit-appearance: none
	}

	::-webkit-file-upload-button {
		-webkit-appearance: button;
		font: inherit
	}

	details {
		display: block
	}

	summary {
		display: list-item
	}

	[hidden],
	template {
		display: none
	}

	@font-face {
		font-family: univers;
		font-weight: 300;
		font-style: normal;
		src: url(/themes/cam/sass/base/fonts/Univers/UniversLTStd-Light.eot);
		src: url(/themes/cam/sass/base/fonts/Univers/UniversLTStd-Light.woff2) format("woff2"), url(/themes/cam/sass/base/fonts/Univers/UniversLTStd-Light.woff) format("woff"), url(/themes/cam/sass/base/fonts/Univers/UniversLTStd-Light.ttf) format("truetype")
	}

	@font-face {
		font-family: univers;
		font-weight: 400;
		font-style: normal;
		src: url(/themes/cam/sass/base/fonts/Univers/UniversLTStd.eot);
		src: url(/themes/cam/sass/base/fonts/Univers/UniversLTStd.woff2) format("woff2"), url(/themes/cam/sass/base/fonts/Univers/UniversLTStd.woff) format("woff"), url(/themes/cam/sass/base/fonts/Univers/UniversLTStd.ttf) format("truetype")
	}

	@font-face {
		font-family: univers;
		font-weight: 700;
		font-style: normal;
		src: url(/themes/cam/sass/base/fonts/Univers/UniversLTStd-Bold.eot);
		src: url(/themes/cam/sass/base/fonts/Univers/UniversLTStd-Bold.woff2) format("woff2"), url(/themes/cam/sass/base/fonts/Univers/UniversLTStd-Bold.woff) format("woff"), url(/themes/cam/sass/base/fonts/Univers/UniversLTStd-Bold.ttf) format("truetype")
	}

	@-ms-viewport {
		width: auto !important
	}

	@-o-viewport {
		width: auto !important
	}

	@viewport {
		width: auto !important
	}

	* {
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		margin: 0;
		padding: 0
	}

	html {
		overflow-x: hidden;
		font-size: 10px
	}

	body {
		margin: 0;
		color: #71716f;
		font-size: 1.6rem;
		font-family: univers, Helvetica Neue, helvetica, Arial, sans-serif;
		line-height: 1.5em;
		letter-spacing: .03em
	}

	body.mobile_nav__open {
		position: fixed;
		overflow-y: hidden;
		max-width: 100vw
	}

	@media screen and (min-width:64em) {
		body.mobile_nav__open {
			position: static;
			overflow-y: visible;
			max-width: none
		}
	}

	a {
		text-decoration: none;
		-webkit-transition: color .3s ease;
		transition: color .3s ease;
		-webkit-text-decoration-skip: ink;
		text-decoration-skip-ink: auto
	}

	a:link,
	a:visited {
		color: #5a7826
	}

	a:active,
	a:hover {
		color: #93bf37;
		text-decoration: underline
	}

	@media screen and (min-width:64em) {
		a[href^="tel:"] {
			pointer-events: none !important
		}

		a[href^="tel:"]:active,
		a[href^="tel:"]:hover,
		a[href^="tel:"]:link,
		a[href^="tel:"]:visited {
			color: #757575 !important;
			text-decoration: none !important
		}
	}

	.content-area a:not(.quicktabs-loaded):not(.clear-filter):not(.button) {
		text-decoration: underline
	}

	ol,
	ul {
		margin-left: 0;
		padding-left: 0
	}

	img {
		display: block;
		max-width: 100%;
		height: auto
	}

	.page_block__image,
	.page_block__image_inner {
		position: relative;
		height: 100%
	}

	.page_block__image_inner {
		z-index: -1;
		overflow: hidden;
		max-height: 20rem
	}

	.page_block__image_inner p {
		display: none
	}

	.page_block__image_inner img {
		-o-object-fit: cover;
		object-fit: cover;
		width: 100%;
		height: 20rem
	}

	@media screen and (min-width:35em) {
		.page_block__image_inner img {
			height: 30rem
		}
	}

	@media screen and (min-width:46.875em) {
		.page_block__image_inner img {
			height: 100%
		}
	}

	@media screen and (min-width:35em) {
		.page_block__image_inner {
			max-height: 30rem
		}
	}

	@media screen and (min-width:46.875em) {
		.page_block__image_inner {
			max-height: 100%
		}
	}

	.icon-bg-angle {
		position: absolute;
		top: -5%;
		width: 100%;
		height: 110%;
		pointer-events: none;
		fill: #fff
	}

	table {
		width: 100%;
		margin-right: -1rem;
		margin-left: -1rem;
		border-collapse: collapse;
		table-layout: fixed
	}

	tr {
		border-bottom: .1rem solid #ccd8c7
	}

	th {
		text-align: left
	}

	td,
	th {
		padding: 1rem
	}

	td,
	td * {
		vertical-align: top
	}

	.table-wrapper {
		overflow-x: auto;
		width: 100%;
		margin-top: 2rem;
		margin-bottom: 2rem;
		-ms-overflow-style: -ms-autohiding-scrollbar
	}

	.table-wrapper+h1,
	.table-wrapper+h2,
	.table-wrapper+h3,
	.table-wrapper+h4,
	.table-wrapper+h5,
	.table-wrapper+h6 {
		margin-top: 4rem
	}

	.table-wrapper+p {
		margin-top: 2rem
	}

	tr.even td,
	tr.odd td {
		padding: 1rem .5rem
	}

	tr.odd {
		background-color: #fff
	}

	code,
	pre,
	tt {
		font: 1em andale mono, lucida console, monospace;
		line-height: 1.5
	}

	pre {
		display: block;
		margin: .5rem 0;
		padding: .5rem;
		border: .1rem solid #aaa;
		background-color: #efefef
	}

	ul,
	ul li,
	ul li.leaf,
	ul li ul,
	ul li ul li {
		list-style: none
	}

	ol {
		margin-left: 3rem;
		list-style-type: decimal
	}

	ol ol {
		list-style-type: lower-alpha
	}

	ol ol ol {
		list-style-type: decimal
	}

	.content-area ol,
	.content-area ul:not(.tab-list):not(.landing__link_list):not(.search-result):not(.quicktabs-tabs):not(.pager__items):not(.date-recur-occurrences):not(.filters-list) {
		margin-bottom: 2rem;
		margin-left: 3rem
	}

	.content-area ol li+li,
	.content-area ol li ol,
	.content-area ol li ul,
	.content-area ul:not(.tab-list):not(.landing__link_list):not(.search-result):not(.quicktabs-tabs):not(.pager__items):not(.date-recur-occurrences):not(.filters-list) li+li,
	.content-area ul:not(.tab-list):not(.landing__link_list):not(.search-result):not(.quicktabs-tabs):not(.pager__items):not(.date-recur-occurrences):not(.filters-list) li ol,
	.content-area ul:not(.tab-list):not(.landing__link_list):not(.search-result):not(.quicktabs-tabs):not(.pager__items):not(.date-recur-occurrences):not(.filters-list) li ul {
		margin-top: .5rem
	}

	.content-area ul:not(.tab-list):not(.landing__link_list):not(.search-result):not(.quicktabs-tabs):not(.pager__items):not(.date-recur-occurrences):not(.filters-list) li {
		list-style: disc
	}

	#block-cam-local-tasks {
		margin-top: 4rem
	}

	#block-cam-local-tasks .tabs.container {
		margin-bottom: 4rem
	}

	@media screen and (min-width:64em) {
		.front #block-cam-local-tasks .tabs.container {
			margin-left: 8rem
		}
	}

	.page-programs-courses-find-program #block-cam-local-tasks {
		margin-right: 4rem;
		margin-left: 4rem;
		padding-top: 4rem
	}

	hr {
		max-width: 100%;
		height: .1rem;
		margin-top: 4rem;
		margin-bottom: 4rem;
		border: none;
		background-color: #f0f2f7
	}

	div:not(.hero-image):not(.ajax-progress):empty {
		display: none
	}

	abbr {
		border-bottom: .1rem dotted #666;
		white-space: nowrap;
		cursor: help
	}

	#edit-title {
		width: 99%;
		font-size: 2.4rem
	}

	#system-themes-form img {
		width: 110rem
	}

	.form-item .description {
		margin-top: .5rem;
		color: #777;
		font-style: italic;
		font-size: .8em;
		line-height: 1.2em
	}

	#edit-delete {
		color: #c00
	}

	div.messages {
		margin: 1em 0;
		padding: 1rem;
		border: .1rem solid #ace;
		background: #bdf;
		color: #036
	}

	div.warning {
		border-color: #ed5;
		background: #fe6;
		color: #840
	}

	div.error {
		border-color: #d52;
		background: #e63;
		color: #fff
	}

	div.status {
		border-color: #be7;
		background: #cf8;
		color: #360
	}

	#skip a:hover,
	#skip a:link,
	#skip a:visited {
		position: absolute;
		top: auto;
		left: -100rem;
		overflow: hidden;
		width: .1rem;
		height: .1rem
	}

	#skip a:active,
	#skip a:focus {
		position: static;
		width: auto;
		height: auto
	}

	div.view div.views-admin-links {
		width: auto
	}

	div.contextual-links-wrapper {
		z-index: 500
	}

	div.block {
		position: relative
	}

	div.block .edit {
		position: absolute;
		top: -.5rem;
		right: -2rem;
		z-index: 40;
		display: none;
		padding: .3rem .8rem 0;
		border: .1rem solid #ccc;
		border-radius: .3rem;
		background-color: #fff;
		-webkit-box-shadow: 0 .1rem .3rem #888;
		box-shadow: 0 .1rem .3rem #888;
		font-size: 1rem;
		line-height: 1.6rem
	}

	div.block:hover .edit {
		display: block
	}

	div.workbench-info-block {
		margin-bottom: 1rem;
		padding: .5rem 1rem;
		border-style: solid;
		border-radius: .25rem
	}

	.mobile-only {
		display: block;
		visibility: visible
	}

	@media screen and (min-width:64em) {
		.mobile-only {
			display: none !important;
			visibility: hidden
		}
	}

	.desktop-only {
		display: none;
		visibility: hidden
	}

	@media screen and (min-width:64em) {
		.desktop-only {
			display: block;
			visibility: visible
		}
		span.desktop-only {
			display: inline-block;
		}
	}

	#site-name {
		margin: 0;
		padding: 0 0 .5em;
		font-weight: 300;
		font-size: 2.2em;
		line-height: 1.3em
	}

	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		margin-top: 0;
		color: #4d4d4c;
		font-weight: 700;
		font-family: lexia, Helvetica Neue, helvetica, Arial, sans-serif;
		line-height: 1.2
	}

	h1.homepage__title {
		position: relative;
		width: -webkit-fit-content;
		width: -moz-fit-content;
		width: fit-content;
		margin-right: 6rem;
		margin-left: 6rem;
		font-size: 2.2rem;
		line-height: 1.4;
		letter-spacing: -.013rem;
		text-align: center
	}

	h1.homepage__title:before {
		content: "";
		position: absolute;
		top: .8rem;
		left: -2.8rem;
		border-bottom: 2.6rem solid transparent;
		border-left: 1.5rem solid #93bf37
	}

	@media screen and (min-width:30em) {
		h1.homepage__title:before {
			top: 1rem
		}
	}

	@media screen and (min-width:40em) {
		h1.homepage__title:before {
			top: 1.4rem;
			left: -3.8rem;
			border-bottom: 3.2rem solid transparent;
			border-left: 2.2rem solid #93bf37
		}
	}

	@media screen and (min-width:30em) {
		h1.homepage__title {
			margin-right: 6rem;
			margin-left: 6rem;
			font-size: 3rem
		}
	}

	@media screen and (min-width:40em) {
		h1.homepage__title {
			margin-right: auto;
			margin-left: auto;
			font-size: 4rem
		}
	}

	@media screen and (min-width:64em) {
		h1.homepage__title {
			margin-right: 0;
			margin-left: 0;
			text-align: left
		}
	}

	@media screen and (min-width:75em) {
		h1.homepage__title {
			margin-left: 0
		}
	}

	h1 {
		margin-bottom: 3rem;
		font-size: 2.8rem
	}

	@media screen and (min-width:40em) {
		h1 {
			font-size: 3.8rem
		}
	}

	@media screen and (min-width:67.5em) {
		h1 {
			font-size: 4.6rem
		}
	}

	.page_title {
		margin-bottom: 4rem;
		font-size: 3.8rem;
		line-height: 1.26;
		letter-spacing: -.012rem
	}

	.page_title+.subtitle {
		margin-top: -3rem;
		margin-bottom: 3rem
	}

	@media screen and (min-width:53.75em) {
		.page_title {
			font-size: 4.6rem;
			font-family: lexia, Helvetica Neue, helvetica, Arial, sans-serif;
			line-height: 1.17;
			letter-spacing: -.02rem
		}
	}

	@media screen and (min-width:75em) {
		.page_title {
			margin-bottom: 8rem
		}

		.page_title+.subtitle {
			margin-top: -7.5rem
		}
	}

	@media screen and (min-width:64em) {

		.page-node-type-landing-page .page_title,
		.page-node-type-listing-page .page_title,
		.page-node-type-student-funding .page_title {
			font-size: 6.4rem
		}
	}

	.subtitle,
	.subtitle .text {
		max-width: none;
		color: #4d4d4c;
		font-weight: 300;
		font-size: 2rem;
		font-family: lexia, Helvetica Neue, helvetica, Arial, sans-serif;
		letter-spacing: -.015rem
	}

	@media screen and (min-width:64em) {

		.subtitle,
		.subtitle .text {
			font-size: 2.6rem
		}
	}

	.subtitle p {
		margin-bottom: 0
	}

	h2 {
		margin-bottom: 4rem;
		font-size: 2.4rem
	}

	h2+h1,
	h2+h2,
	h2+h3,
	h2+h4,
	h2+h5,
	h2+h6 {
		margin-top: -2rem
	}

	@media screen and (min-width:40em) {
		h2 {
			font-size: 2.9rem
		}
	}

	@media screen and (min-width:75em) {
		h2 {
			font-size: 3.4rem
		}
	}

	.main_content h2 {
		position: relative
	}

	.main_content h2:before {
		content: "";
		position: absolute;
		top: .6rem;
		left: -1.8rem;
		border-bottom: 1.6rem solid transparent;
		border-left: 1.1rem solid #93bf37
	}

	@media screen and (min-width:75em) {
		.main_content h2:before {
			top: .8rem;
			left: -3.8rem;
			border-bottom: 3.2rem solid transparent;
			border-left: 2.2rem solid #93bf37
		}
	}

	.page-node-type-landing-page .main_content h2 {
		font-size: 2.6rem
	}

	@media screen and (min-width:64em) {
		.page-node-type-landing-page .main_content h2 {
			font-size: 4rem
		}
	}

	h3 {
		margin-bottom: 1.5rem;
		font-size: 2rem
	}

	@media screen and (min-width:40em) {
		h3 {
			font-size: 2.2rem
		}
	}

	@media screen and (min-width:67.5em) {
		h3 {
			font-size: 2.4rem
		}
	}

	h4 {
		font-size: 1.8rem
	}

	h4,
	h5 {
		margin-bottom: 1.25rem
	}

	h5 {
		font-size: 1.6rem
	}

	h6 {
		margin-bottom: 1.25rem;
		font-size: 1.5rem;
		text-transform: uppercase
	}

	p {
		margin-bottom: 1rem;
		font-size: 1.6rem;
		font-family: univers, Helvetica Neue, helvetica, Arial, sans-serif;
		line-height: 1.4
	}

	p:not(.visually-hidden)+h1,
	p:not(.visually-hidden)+h2,
	p:not(.visually-hidden)+h3,
	p:not(.visually-hidden)+h4,
	p:not(.visually-hidden)+h5,
	p:not(.visually-hidden)+h6 {
		margin-top: 4rem
	}

	p:empty {
		display: none
	}

	.main_content p {
		max-width: 64rem
	}

	.main_content ol,
	.main_content ul {
		max-width: 61rem
	}

	strong:empty {
		display: none
	}

	a {
		word-wrap: break-word
	}

	p.lead {
		margin-bottom: 1rem;
		font-weight: 300;
		font-size: 1.375rem;
		font-family: univers, Helvetica Neue, helvetica, Arial, sans-serif;
		line-height: 1.6
	}

	.image_caption {
		margin-top: 1rem;
		font-size: 1.2rem
	}

	.page-node-type-page .intro_text blockquote,
	blockquote {
		position: relative;
		margin-top: 2rem;
		margin-bottom: 2rem;
		padding-top: 2rem;
		padding-bottom: 3rem;
		padding-left: 2rem;
		font-size: 1.6rem
	}

	.page-node-type-page .intro_text blockquote:before,
	blockquote:before {
		content: "";
		position: absolute;
		top: 0;
		left: -.5rem;
		height: calc(100% - .5rem);
		border-right: .5rem solid #93bf37;
		border-bottom: .5rem solid #fff
	}

	.page-node-type-page .intro_text blockquote p,
	blockquote p {
		color: #71716f;
		font-weight: 400;
		font-size: 1.6rem;
		letter-spacing: normal;
		text-transform: none
	}

	.page-node-type-page .intro_text blockquote .blockquote-attr,
	blockquote .blockquote-attr {
		color: #9a9a9a;
		font-weight: 700;
		font-size: 1.4rem;
		letter-spacing: .053rem;
		text-transform: uppercase
	}

	@media screen and (min-width:64em) {

		.page-node-type-page .intro_text blockquote,
		blockquote {
			margin-top: 4rem;
			margin-bottom: 4rem
		}
	}

	.field-content a {
		color: #006097;
		font-size: 1.25rem
	}

	.svg-angle-right,
	.svg-bg-angle,
	.svg-exclamation-triangle,
	.svg-facebook,
	.svg-instagram,
	.svg-linkedin,
	.svg-right-angle-green,
	.svg-search,
	.svg-share-alt,
	.svg-twitter,
	.svg-youtube {
		background: url(/themes/cam/css/svg/sprite.view.svg) no-repeat
	}

	.svg-angle-right {
		background-position: 0 0
	}

	.svg-angle-right-dims {
		width: 16px;
		height: 32px
	}

	.svg-bg-angle {
		background-position: 63.05004597399186% 0
	}

	.svg-bg-angle-dims {
		width: 21.87px;
		height: 32px
	}

	.svg-exclamation-triangle {
		background-position: 24.242424242424242% 0
	}

	.svg-exclamation-triangle-dims {
		width: 32px;
		height: 28.44px
	}

	.svg-facebook {
		background-position: 94.41891891891892% 50%
	}

	.svg-facebook-dims {
		width: 24px;
		height: 24px
	}

	.svg-instagram {
		background-position: 0 100%
	}

	.svg-instagram-dims {
		width: 24px;
		height: 24px
	}

	.svg-linkedin {
		background-position: 97.29729729729729% 100%
	}

	.svg-linkedin-dims {
		width: 24px;
		height: 24px
	}

	.svg-right-angle-green {
		background-position: 0 57.142857142857146%
	}

	.svg-right-angle-green-dims {
		width: 22.15px;
		height: 32px
	}

	.svg-search {
		background-position: 33.56060606060606% 57.142857142857146%
	}

	.svg-search-dims {
		width: 32px;
		height: 32px
	}

	.svg-share-alt {
		background-position: 99.81428571428572% 0
	}

	.svg-share-alt-dims {
		width: 28px;
		height: 32px
	}

	.svg-twitter {
		background-position: 64.86486486486487% 100%
	}

	.svg-twitter-dims {
		width: 24px;
		height: 24px
	}

	.svg-youtube {
		background-position: 32.432432432432435% 100%
	}

	.svg-youtube-dims {
		width: 24px;
		height: 24px
	}

	.ajax-progress-fullscreen,
	.ajax-progress-throbber,
	.btn,
	.btn-signIn,
	.button,
	.landing__link a,
	.main_content .btn,
	.main_content .button,
	.main_content button,
	.main_content input[type=submit],
	.program__content .program-tabs.mobile-tabs,
	.program__content .program-tabs.mobile-tabs:before,
	.share-list-item,
	.share-list-item-link,
	.wf-loading,
	button,
	h1,
	h2,
	h3,
	input[type=submit],
	ul.quicktabs-tabs.mobile-tabs,
	ul.quicktabs-tabs.mobile-tabs:before {
		-webkit-transition: all .3s ease;
		transition: all .3s ease
	}

	a,
	blockquote,
	h1,
	h2,
	h3,
	h4,
	h5,
	h6,
	li,
	p,
	span {
		text-rendering: optimizeLegibility;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale
	}

	.btn,
	.button,
	button,
	input[type=submit] {
		font-size: 1.6rem;
		font-weight: 700;
		line-height: 1;
		display: inline-block;
		padding: 2rem 2rem 1.6rem;
		text-align: center;
		letter-spacing: .06rem;
		text-transform: uppercase;
		border: 0;
		background-color: #5a7826
	}

	.btn,
	.btn:link,
	.btn:visited,
	.button,
	.button:link,
	.button:visited,
	button,
	button:link,
	button:visited,
	input[type=submit],
	input[type=submit]:link,
	input[type=submit]:visited {
		text-decoration: none;
		color: #fff
	}

	.btn:hover,
	.button:hover,
	button:hover,
	input[type=submit]:hover {
		background-color: #93bf37
	}

	@media screen and (min-width:20em) {

		.btn,
		.button,
		button,
		input[type=submit] {
			padding-right: 4rem;
			padding-left: 4rem;
			width: 100%;
		}
	}

		@media screen and (min-width:46em) {

		.btn,
		.button,
		button,
		input[type=submit] {
			padding-right: 4rem;
			padding-left: 4rem;
			width: 30vw;
		}
	}

			@media screen and (min-width:64em) {

		.btn,
		.button,
		button,
		input[type=submit] {
			padding-right: 4rem;
			padding-left: 4rem;
			width: 20vw;
		}
	}

	.main_content .btn,
	.main_content .button,
	.main_content button,
	.main_content input[type=submit] {
		font-size: 1.6rem;
		font-weight: 700;
		line-height: 1;
		display: inline-block;
		padding: 2rem 2rem 1.6rem;
		text-align: center;
		letter-spacing: .06rem;
		text-transform: uppercase;
		border: 0;
		background-color: #5a7826;
		-webkit-box-shadow: .4rem 1.6rem 1.8rem 0 rgba(61, 61, 61, .2);
		box-shadow: .4rem 1.6rem 1.8rem 0 rgba(61, 61, 61, .2)
	}

	.main_content .btn,
	.main_content .btn:link,
	.main_content .btn:visited,
	.main_content .button,
	.main_content .button:link,
	.main_content .button:visited,
	.main_content button,
	.main_content button:link,
	.main_content button:visited,
	.main_content input[type=submit],
	.main_content input[type=submit]:link,
	.main_content input[type=submit]:visited {
		text-decoration: none;
		color: #fff
	}

	.main_content .btn:hover,
	.main_content .button:hover,
	.main_content button:hover,
	.main_content input[type=submit]:hover {
		background-color: #93bf37
	}

	@media screen and (min-width:30em) {

		.main_content .btn,
		.main_content .button,
		.main_content button,
		.main_content input[type=submit] {
			padding-right: 4rem;
			padding-left: 4rem;
			min-width: 160px;
			margin-bottom: 2rem;
		}
	}

	.btn--black {
		color: #fff;
		background: #222
	}

	.btn--full {
		font-size: 1.25rem;
		display: block;
		width: 100%;
		padding: 1rem 0
	}

	.btn-signIn {
		font-size: 1rem;
		font-weight: 500;
		margin: 1rem;
		padding: 1rem 1.5rem;
		text-align: center;
		text-decoration: none;
		letter-spacing: .1rem;
		color: #006097;
		border: .1rem solid #006097;
		border-radius: .25rem;
		background: #fff
	}

	.btn-signIn:hover {
		color: #fff;
		background: #006097
	}

	form {
		max-width: 100%
	}

	form label+input {
		margin-top: .5rem
	}

	input:not([type=submit]) {
		max-width: 100%;
		height: 4rem;
		padding: 1.4rem 4.2rem 1.2rem 1.1rem;
		border: .1rem solid #9a9a9a;
		border-radius: .2rem;
		-webkit-box-shadow: .4rem .06rem 1rem 0 rgba(0, 0, 0, .11);
		box-shadow: .4rem .06rem 1rem 0 rgba(0, 0, 0, .11);
		font-size: 1.4rem;
		line-height: 1.2
	}

	input:not([type=submit])::-ms-clear {
		display: none
	}

	input[type=submit] {
		cursor: pointer
	}

	input[type=password] {
		letter-spacing: .25rem
	}

	.form-item:first-child+.form-actions .form-submit {
		margin-top: 0
	}

	.form-item label {
		display: block
	}

	.form-item+.form-item {
		margin-top: 2rem
	}

	.left .form-item+.form-item {
		margin-top: 0
	}

	input[type=checkbox],
	input[type=radio] {
		position: absolute;
		left: 0;
		width: auto;
		height: auto;
		margin-top: 0;
		margin-left: -2rem;
		opacity: 1e-8
	}

	input[type=checkbox]+label,
	input[type=radio]+label {
		position: relative;
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: start;
		-ms-flex-align: start;
		align-items: flex-start;
		width: 100%;
		min-height: 3.4rem;
		margin-bottom: 0;
		padding-left: 3.4rem;
		color: #71716f;
		font-weight: 400;
		font-size: 1.6rem;
		line-height: 1.5;
		letter-spacing: -.02rem;
		cursor: pointer
	}

	input[type=checkbox]+label span,
	input[type=radio]+label span {
		position: absolute;
		top: 50%;
		-webkit-transform: translateY(-50%);
		transform: translateY(-50%)
	}

	input[type=checkbox]+label:before,
	input[type=radio]+label:before {
		content: "";
		position: absolute;
		top: -.3rem;
		left: 0;
		width: 1.8rem;
		height: 1.8rem;
		margin: .4rem;
		border: .1rem solid #e4e4e4;
		border-radius: .2rem;
		background-color: #f6f6f6;
		-webkit-transition: -webkit-transform .28s ease;
		transition: -webkit-transform .28s ease;
		transition: transform .28s ease;
		transition: transform .28s ease, -webkit-transform .28s ease
	}

	input[type=checkbox]+label:after,
	input[type=radio]+label:after {
		content: "";
		position: absolute;
		top: .3rem;
		left: .6rem;
		display: block;
		width: 1.4rem;
		height: 1.4rem;
		border-radius: .2rem
	}

	input[type=checkbox]:checked+label:after,
	input[type=radio]:checked+label:after {
		background-color: #5a7826
	}

	input[type=checkbox]:focus+label:before,
	input[type=radio]:focus+label:before {
		outline: 0
	}

	input[type=radio]+label:after,
	input[type=radio]+label:before {
		border-radius: 50%
	}

	.form-checkbox,
	.form-checkboxes,
	.form-radios {
		margin-top: 2.5rem
	}

	.form-type-select {
		width: 100%;
		margin-bottom: 4rem
	}

	.form-type-select select {
		width: 100%;
		height: 4rem;
		padding: 1.1rem 1rem;
		border: none;
		background-color: #f6f6f6;
		background-image: url(/themes/cam/images/select-arrow.svg);
		background-position: right 1rem center;
		background-size: 1.5rem;
		background-repeat: no-repeat;
		color: #71716f;
		-webkit-box-shadow: .4rem .06rem 1rem 0 rgba(0, 0, 0, .11);
		box-shadow: .4rem .06rem 1rem 0 rgba(0, 0, 0, .11);
		text-indent: .1rem;
		text-overflow: "";
		cursor: pointer;
		-webkit-appearance: none;
		-moz-appearance: none
	}

	.form-item-field-deadline-value-1 {
		margin-bottom: .8rem
	}

	.form-item-field-deadline-value-1 label {
		margin-bottom: 1.2rem
	}

	.form-item.collapsible {
		margin-top: 0;
		padding-top: 2rem;
		padding-bottom: 2rem;
		border-top: .1rem solid #cfd8c7
	}

	.form-item.collapsible>span {
		position: relative;
		display: block;
		width: 100%;
		color: #4d4d4c;
		outline: 0;
		list-style-type: none;
		font-weight: 600;
		font-size: 2rem;
		font-family: lexia, Helvetica Neue, helvetica, Arial, sans-serif;
		line-height: 1.8;
		letter-spacing: -.017rem;
		cursor: pointer
	}

	.form-item.collapsible>span:after,
	.form-item.collapsible>span:before {
		content: "";
		position: absolute;
		right: 0;
		width: 4rem;
		height: 4rem
	}

	.form-item.collapsible>span:before {
		top: -.2rem;
		background-color: #5a7826
	}

	.form-item.collapsible>span:after {
		top: -.1rem;
		display: inline-block;
		background-image: url(/themes/cam/images/down-arrow.svg);
		background-position: 50%;
		background-size: 2rem;
		background-repeat: no-repeat;
		-webkit-transition: -webkit-transform .3s ease;
		transition: -webkit-transform .3s ease;
		transition: transform .3s ease;
		transition: transform .3s ease, -webkit-transform .3s ease
	}

	.form-item.collapsible>div {
		overflow-y: hidden;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		height: 0;
		margin-top: 0;
		opacity: 0;
		-webkit-transition: height .3s ease;
		transition: height .3s ease
	}

	.form-item.collapsible>div div:first-child {
		margin-top: 2rem
	}

	.form-item.collapsible.open>div {
		height: auto;
		opacity: 1
	}

	.form-item.collapsible.open>span:after {
		-webkit-transform: rotate(-180deg);
		transform: rotate(-180deg)
	}

	.form-actions input+input,
	.form-actions input+input.form-submit,
	.form-submit {
		margin-top: 4rem
	}

	.site_search {
		position: relative;
		margin-right: 4rem;
		margin-left: 4rem
	}

	.site_search.is-active {
		display: block
	}

	.site_search input[type=text] {
		width: 100%;
		height: 4rem;
		margin-top: 0;
		border: .1rem solid #779f32;
		border-radius: .3rem;
		background-color: #fff;
		-webkit-box-shadow: .4rem .6rem 1rem 0 rgba(0, 0, 0, .11);
		box-shadow: .4rem .6rem 1rem 0 rgba(0, 0, 0, .11);
		font-size: 1.2rem
	}

	@media screen and (min-width:64em) {
		.site_search input[type=text] {
			width: 17.7rem
		}
	}

	.site_search input[type=text]::-webkit-search-cancel-button {
		-webkit-appearance: none
	}

	.site_search .search_submit {
		position: absolute;
		top: 0;
		right: 0;
		width: 4rem;
		height: 4rem;
		margin: 0;
		padding: 0;
		background-color: transparent;
		font-size: 0;
		cursor: pointer;
		-webkit-transition: -webkit-transform .5s ease;
		transition: -webkit-transform .5s ease;
		transition: transform .5s ease;
		transition: transform .5s ease, -webkit-transform .5s ease
	}

	.site_search .search_submit:hover {
		-webkit-transform: scale(1.2);
		transform: scale(1.2)
	}

	.site_search .search_submit svg {
		width: 2rem;
		height: 2rem;
		fill: #5a7826
	}

	@media screen and (min-width:64em) {
		.site_search {
			margin: 0
		}
	}

	.mobile_nav__wrapper .site_search {
		position: relative
	}

	.content-area #onpointsearch-root #onpointsearch-container-header #onpointsearch-container-header-input #onpointsearch-query {
		padding-top: 1.1rem !important;
		font-size: 1.8rem !important
	}

	.content-area #onpointsearch-root #onpointsearch-container-header #onpointsearch-container-header-input #onpointsearch-submit {
		-webkit-transition: all .3s ease !important;
		transition: all .3s ease !important
	}

	.content-area #onpointsearch-root #onpointsearch-filters-wrapper .onpointsearch-filters .btn--apply-filters {
		display: inline-block !important;
		padding: 1.4rem .6rem 1rem !important;
		border: 0 !important;
		border-radius: 0 !important;
		background: #5a7826 !important;
		font-weight: 700 !important;
		font-size: 1.4rem !important;
		line-height: 1 !important;
		letter-spacing: .06rem !important;
		text-align: center !important;
		text-transform: uppercase !important;
		-webkit-transition: all .3s ease !important;
		transition: all .3s ease !important
	}

	.content-area #onpointsearch-root #onpointsearch-filters-wrapper .onpointsearch-filters .btn--apply-filters,
	.content-area #onpointsearch-root #onpointsearch-filters-wrapper .onpointsearch-filters .btn--apply-filters:link,
	.content-area #onpointsearch-root #onpointsearch-filters-wrapper .onpointsearch-filters .btn--apply-filters:visited {
		color: #fff !important;
		text-decoration: none !important
	}

	.content-area #onpointsearch-root #onpointsearch-filters-wrapper .onpointsearch-filters .btn--apply-filters:hover {
		background-color: #93bf37 !important
	}

	.quicktabs-wrapper {
		margin-right: 1rem;
		margin-left: 1rem
	}

	@media screen and (min-width:64em) {
		.quicktabs-wrapper {
			margin-right: 0;
			margin-left: 0
		}
	}

	ul.quicktabs-tabs {
		line-height: 1.1;
		position: relative;
		display: inline-block;
		width: 100%;
		list-style: none;
		white-space: nowrap;
		letter-spacing: normal;
		-ms-overflow-style: -ms-autohiding-scrollbar
	}

	ul.quicktabs-tabs.mobile-tabs {
		overflow-x: auto;
		margin-bottom: -1rem;
		padding-top: 3.5rem
	}

	ul.quicktabs-tabs.mobile-tabs:before {
		font-size: 1rem;
		position: absolute;
		z-index: 2;
		top: .5rem;
		left: -1rem;
		display: block;
		width: calc(100% + 1rem);
		content: "\02190\02002Scroll for more\02002\02192";
		text-align: center;
		text-transform: uppercase;
		color: #9a9a9a
	}

	ul.quicktabs-tabs li {
		position: relative;
		display: inline-block;
		padding: 0
	}

	ul.quicktabs-tabs li a {
		font-size: 1.6rem;
		font-weight: 400;
		position: relative;
		display: inline-block;
		padding: 2rem 3rem;
		text-decoration: none;
		text-transform: none;
		color: #5a7826;
		outline: none;
		background-color: transparent;
		-webkit-box-shadow: none;
		box-shadow: none
	}

	@media screen and (min-width:40em) {
		ul.quicktabs-tabs li a {
			padding-top: 4rem;
			padding-bottom: 3.5rem
		}
	}

	@media screen and (min-width:46.875em) {
		ul.quicktabs-tabs li a {
			font-size: 1.8rem
		}
	}

	@media screen and (min-width:64em) {
		ul.quicktabs-tabs li a {
			font-size: 2rem;
			padding-right: 4rem;
			padding-left: 4rem
		}
	}

	ul.quicktabs-tabs .active a {
		color: #71716f;
		background-color: #fff
	}

	.quicktabs-block-title {
		display: none
	}

	.quicktabs-main {
		overflow: hidden;
		padding: 4rem 3rem;
		background-color: #fff
	}

	@media screen and (min-width:64em) {
		.quicktabs-main {
			padding: 6rem 4rem
		}
	}

	.ajax-progress-fullscreen,
	.ajax-progress-throbber {
		position: fixed;
		z-index: 1000;
		top: 0;
		left: 0;
		width: 100vw;
		height: 100vh;
		margin: 0;
		padding: 0;
		opacity: 1;
		border-radius: 0;
		background: rgba(61, 61, 61, .6) url(/themes/cam/images/ajax-loader.svg) 50% no-repeat;
		background-size: 15rem
	}

	.ajax-progress-fullscreen .message,
	.ajax-progress-fullscreen .throbber,
	.ajax-progress-throbber .message,
	.ajax-progress-throbber .throbber {
		display: none
	}

	.breadcrumb {
		margin-bottom: 4rem
	}

	.breadcrumb ol {
		position: relative;
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		margin-left: -1rem;
		list-style-type: none
	}

	@media screen and (max-width:64em) {
		.breadcrumb ol {
			z-index: 2;
			width: 100%
		}
	}

	.breadcrumb li {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		padding-left: 1rem;
		font-size: 1.2rem;
		line-height: 1.5
	}

	.breadcrumb li:last-child {
		padding-left: 1rem;
		color: #71716f
	}

	@media screen and (min-width:64em) {
		.breadcrumb li {
			font-size: 1.6rem
		}
	}

	.breadcrumb .sep {
		padding-left: 1rem
	}

	.breadcrumb a {
		color: #5a7826;
		-webkit-transition-duration: .3s;
		transition-duration: .3s;
		-webkit-transition-property: color;
		transition-property: color
	}

	.breadcrumb a:focus,
	.breadcrumb a:hover {
		color: #93bf37
	}

	.breadcrumb svg {
		width: 1.6rem;
		height: 1.6rem;
		margin-top: -.2rem;
		fill: #71716f
	}

	@media screen and (min-width:64em) {
		.breadcrumb svg {
			width: 1.9rem;
			height: 1.9rem;
			margin-top: -.4rem
		}
	}

	.tabs {
		padding-top: 1.25rem;
		padding-bottom: 1rem;
		border-top: .1rem solid #f0f2f7;
		border-bottom: .1rem solid #f0f2f7
	}

	.tabs .tab-item {
		line-height: 1;
		display: inline-block;
		padding: 0 1rem 0 0
	}

	.tabs .tab-item a {
		color: #006097
	}

	.share {
		position: relative;
		top: .8rem;
		left: 0;
		display: inline-block;
		width: 6rem;
		text-align: center
	}

	.share-title {
		font-size: .75rem;
		line-height: 1;
		width: 100%;
		padding: .7rem 0 .6rem;
		text-transform: uppercase;
		color: #fff;
		background: #006097
	}

	.share-list {
		margin: 0;
		padding: 0;
		list-style-type: none
	}

	.share-list-item {
		display: inline-block;
		width: 100%;
		margin-bottom: 0;
		padding: 1rem .5rem;
		border-bottom: .1rem solid rgba(34, 34, 34, .2);
		background: #fff
	}

	.share-list-item:before {
		display: none
	}

	.share-list-item:hover {
		background: rgba(34, 34, 34, .1)
	}

	.share-list-item:hover .share-list-item-link {
		color: #fff
	}

	.share-list-item-link {
		display: block;
		color: #006097
	}

	.share-list-item-link svg {
		width: 3rem;
		vertical-align: top;
		fill: currentColor
	}

	.share-list-item-link svg.twitter {
		color: #55acee
	}

	.share-list-item-link svg.facebook {
		color: #3b5998
	}

	.share-list-item-link svg.google {
		color: #f34a38
	}

	.sitewide_alert__header {
		line-height: 1.4;
		padding-top: 1rem;
		padding-bottom: 1rem;
		color: #fff;
		background-color: #5a7826
	}

	@media screen and (max-width:64em) {
		.sitewide_alert__header .container {
			padding-right: 2rem;
			padding-left: 2rem
		}
	}

	#emergency-alert-red {
		padding: 2rem;
		background-color: #bb3232
	}

	#emergency-alert-red-content {
		background-color: #f5e4e4
	}

	.sitewide_alert__header_inner {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center
	}

	@media screen and (min-width:64em) {
		.sitewide_alert__header_inner {
			max-width: 60rem;
			margin-right: auto;
			margin-left: auto
		}
	}

	.sitewide_alert__icon {
		-ms-flex-negative: 0;
		flex-shrink: 0;
		width: 2.4rem;
		height: 2.4rem;
		fill: #fff
	}

	.sitewide_alert__title {
		font-weight: 700;
		margin-top: .4rem;
		margin-left: 2rem
	}

	.close_alert {
		font-size: 1.2rem;
		font-weight: 300;
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		margin-right: -.8rem;
		margin-left: auto;
		padding: 1rem .8rem .8rem 1rem;
		-webkit-transition-duration: .3s;
		transition-duration: .3s;
		-webkit-transition-property: background-color;
		transition-property: background-color;
		text-transform: none;
		background-color: transparent
	}

	.sitewide_alert__close_icon {
		position: relative;
		display: inline-block;
		width: 2rem;
		height: 2rem;
		margin-top: -.2rem;
		margin-left: .5rem
	}

	.sitewide_alert__close_icon:after,
	.sitewide_alert__close_icon:before {
		position: absolute;
		top: .8rem;
		left: 0;
		width: 2rem;
		height: .4rem;
		content: "";
		background-color: #fff
	}

	.sitewide_alert__close_icon:before {
		-webkit-transform: rotate(45deg);
		transform: rotate(45deg)
	}

	.sitewide_alert__close_icon:after {
		-webkit-transform: rotate(-45deg);
		transform: rotate(-45deg)
	}

	.sitewide_alert__content {
		font-size: 1.4rem;
		line-height: 1.4;
		padding-top: 2rem;
		padding-bottom: 2rem;
		background-color: #ecf5e4
	}

	.sitewide_alert__content p:last-of-type {
		margin-bottom: 0
	}

	@media screen and (max-width:64em) {
		.sitewide_alert__content .container {
			padding-right: 2rem;
			padding-left: 2rem
		}
	}

	.sitewide_alert__content_inner,
	.sitewide_alert__content_inner p {
		color: #6c6c6a
	}

	@media screen and (min-width:64em) {
		.sitewide_alert__content_inner {
			max-width: 60rem;
			margin-right: auto;
			margin-left: auto;
			padding-left: 4.4rem
		}
	}

	.close_alert:hover {
		background-color: rgba(0, 0, 0, .5)
	}

	.sitewide_alert__alert .sitewide_alert__header {
		background-color: #bb3232
	}

	.sitewide_alert__alert .close_alert:hover {
		background-color: #db7878
	}

	.sitewide_alert__alert .sitewide_alert__content {
		background-color: #f5e4e4
	}

	.sitewide_alert__alert a {
		color: #bb3232
	}

	.sitewide_alert__alert a:focus,
	.sitewide_alert__alert a:hover {
		color: #932727
	}

	.page-node-type-alert .content-area .container .page_title {
		display: none
	}

	.section__journey-banners {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-orient: vertical;
		-webkit-box-direction: normal;
		-ms-flex-flow: column;
		flex-flow: column;
		-webkit-box-pack: justify;
		-ms-flex-pack: justify;
		justify-content: space-between;
		margin-bottom: 1rem
	}

	@media screen and (min-width:64em) {
		.section__journey-banners {
			-webkit-box-orient: horizontal;
			-webkit-box-direction: normal;
			-ms-flex-flow: row;
			flex-flow: row
		}
	}

	.section__journey-banners a {
		font-weight: 700;
		line-height: 1.38;
		display: block;
		margin-top: 1rem;
		margin-bottom: 1rem;
		letter-spacing: .06rem;
		text-transform: uppercase
	}

	.section__journey-banners .paragraph--type--camosun-journey-banner {
		text-align: center
	}

	.section__journey-banners .paragraph--type--camosun-journey-banner:not(:last-child) a {
		border-bottom: .1rem solid #ccd8c7
	}

	@media screen and (min-width:64em) {
		.section__journey-banners .paragraph--type--camosun-journey-banner:not(:last-child) a {
			border-bottom: none
		}
	}

	.section__journey-banners .paragraph--type--camosun-journey-banner img {
		display: none
	}

	@media screen and (min-width:64em) {
		.section__journey-banners .paragraph--type--camosun-journey-banner img {
			display: block
		}
	}

	.section__journey-banners .paragraph--type--camosun-journey-banner a {
		margin-top: 0;
		margin-bottom: 0;
		padding-top: 2rem;
		padding-bottom: 2rem
	}

	@media screen and (min-width:64em) {
		.section__journey-banners .paragraph--type--camosun-journey-banner a {
			padding-bottom: 3rem
		}
	}

	@media screen and (min-width:64em) {
		.section__journey-banners .paragraph--type--camosun-journey-banner {
			max-width: 14rem;
			text-align: left
		}
	}

	.field_camosun_journey_banners {
		padding-top: 2rem;
		padding-bottom: 4rem
	}

	@media screen and (min-width:64em) {
		.field_camosun_journey_banners {
			margin-right: 4rem;
			margin-left: 8rem;
			padding-top: 1rem;
			padding-bottom: 0
		}
	}

	@media screen and (min-width:75em) {
		.field_camosun_journey_banners {
			padding-top: 4rem;
			padding-bottom: 4rem
		}
	}

	.block-views-blockevents-listing-block-3,
	.block-views-blocknews-block-2,
	.featured-event-date-listing,
	.field_primary_featured_content,
	.field_secondary_featured_content {
		margin-right: -4rem;
		margin-left: -4rem
	}

	.featured-event-date-listing,
	.field_secondary_featured_content {
		position: relative
	}

	.featured-event-date-listing:before,
	.field_secondary_featured_content:before {
		position: absolute;
		top: 0;
		left: -500%;
		width: 1100%;
		height: 100%;
		content: "";
		background-color: #f5fcef
	}

	@media screen and (min-width:64em) {
		.field_primary_featured_content {
			margin-left: 0
		}
	}

	.primary-featured-content.text_image.text_image__left {
		padding-top: 4rem;
		padding-bottom: 4rem
	}

	.primary-featured-content.text_image.text_image__left .intro_text {
		padding-right: 0;
		padding-left: 0
	}

	.primary-featured-content.text_image.text_image__left .page_block__image {
		width: 100%;
		margin-right: 0;
		margin-left: 0
	}

	@media screen and (min-width:46.875em) {
		.primary-featured-content.text_image.text_image__left .page_block__image {
			width: calc(50% + 2rem)
		}
	}

	@media screen and (min-width:64em) {
		.primary-featured-content.text_image.text_image__left .page_block__image {
			left: -4rem;
			width: calc(60% + 6rem)
		}
	}

	@media screen and (min-width:67.5em) {
		.primary-featured-content.text_image.text_image__left .page_block__image {
			left: -8rem;
			width: calc(70% - 1rem)
		}
	}

	@media screen and (min-width:75em) {
		.primary-featured-content.text_image.text_image__left .page_block__image {
			left: -20rem;
			width: 80%
		}
	}

	.primary-featured-content.text_image.text_image__left .page_block__text {
		margin-right: 4rem;
		margin-left: 4rem
	}

	@media screen and (min-width:46.875em) {
		.primary-featured-content.text_image.text_image__left .page_block__text {
			margin-left: auto
		}
	}

	@media screen and (min-width:64em) {
		.primary-featured-content.text_image.text_image__left .page_block__text {
			width: calc(40% - 4rem);
			max-width: 40rem;
			margin-right: 4rem
		}
	}

	.primary-featured-content.text_image.text_image__left h2 a {
		font-size: 3rem;
		color: #4d4d4c
	}

	.primary-featured-content.text_image.text_image__left .intro_text {
		margin-bottom: 4rem
	}

	.primary-featured-content.text_image.text_image__left .intro_text p {
		font-size: 1.6rem
	}

	.secondary-featured-content {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
		max-width: 100%;
		padding: 6rem 4rem
	}

	.secondary-featured-content+.secondary-featured-content {
		padding-top: 0
	}

	@media screen and (min-width:30em) {
		.secondary-featured-content {
			-webkit-box-align: start;
			-ms-flex-align: start;
			align-items: flex-start;
			-ms-flex-wrap: nowrap;
			flex-wrap: nowrap
		}
	}

	@media screen and (min-width:53.75em) {
		.secondary-featured-content {
			-webkit-box-flex: 1;
			-ms-flex: 1;
			flex: 1
		}

		.secondary-featured-content+.secondary-featured-content {
			padding-top: 6rem
		}

		.secondary-featured-content:first-child {
			padding-right: 2rem;
			padding-left: 4rem
		}

		.secondary-featured-content:last-child {
			padding-right: 4rem;
			padding-left: 2rem
		}
	}

	@media screen and (min-width:64em) {

		.secondary-featured-content,
		.secondary-featured-content+.secondary-featured-content {
			padding-top: 8rem;
			padding-bottom: 7rem
		}

		.secondary-featured-content:first-child {
			padding-right: 2rem
		}

		.secondary-featured-content:last-child {
			padding-left: 2rem
		}
	}

	@media screen and (min-width:75em) {

		.secondary-featured-content,
		.secondary-featured-content+.secondary-featured-content {
			padding-top: 8rem;
			padding-bottom: 7rem
		}

		.secondary-featured-content:first-child {
			padding-right: 2rem
		}

		.secondary-featured-content:last-child {
			padding-left: 2rem
		}
	}

	.secondary-featured-content__image {
		position: relative;
		width: 32rem
	}

	@media screen and (min-width:30em) {
		.secondary-featured-content__image {
			-ms-flex-negative: 0;
			flex-shrink: 0;
			width: 15rem;
			height: 15rem
		}

		.secondary-featured-content__image img {
			height: 100%;
			-o-object-fit: cover;
			object-fit: cover
		}
	}

	@media screen and (min-width:35em) {
		.secondary-featured-content__image {
			width: 17rem;
			height: 17rem
		}
	}

	.secondary-featured-content__image:before {
		position: absolute;
		top: 0;
		left: 0;
		content: "";
		border-bottom: 1rem solid #f5fcef;
		border-right: 1rem solid #f5fcef;
		border-color: #f5fcef transparent transparent #f5fcef;
		border-style: solid;
		border-width: 1rem
	}

	.secondary-featured-content__text {
		max-width: 48rem;
		padding-top: 2rem
	}

	@media screen and (min-width:30em) {
		.secondary-featured-content__text {
			padding-top: 0;
			padding-left: 2rem
		}
	}

	.secondary-featured-content__text .intro_text {
		margin-bottom: 2rem;
		padding-right: 0;
		padding-left: 0
	}

	.secondary-featured-content__text .intro_text p {
		font-size: 1.6rem;
		line-height: 1.38
	}

	.secondary-featured-content__text h2 {
		margin: 0
	}

	.field_secondary_featured_content .container {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
		-webkit-box-pack: justify;
		-ms-flex-pack: justify;
		justify-content: space-between
	}

	.field_secondary_featured_content .container h2 {
		font-size: 1.8rem;
		line-height: 1.5;
		margin-bottom: 2rem;
		letter-spacing: -.015rem
	}

	.field_secondary_featured_content .container h2 a {
		color: #4d4d4c
	}

	.field_secondary_featured_content .container h2:before {
		content: none
	}

	.field_secondary_featured_content .container .intro_text.container {
		display: block;
		overflow: auto;
		-ms-flex-wrap: unset;
		flex-wrap: unset;
		width: auto;
		max-width: unset;
		margin-right: unset;
		margin-left: unset
	}

	.featured-event-date-listing {
		position: relative;
		padding: 6rem 4rem
	}

	.featured-event-date-listing:before {
		position: absolute;
		z-index: -1;
		top: 0;
		left: -500%;
		width: 1100%;
		height: 100%;
		content: "";
		background-color: #f5fcef
	}

	@media screen and (min-width:53.75em) {
		.featured-event-date-listing {
			padding-top: 8rem;
			padding-bottom: 8rem
		}
	}

	@media screen and (min-width:64em) {
		.featured-event-date-listing .views-element-container>div {
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex;
			-ms-flex-wrap: wrap;
			flex-wrap: wrap
		}

		.featured-event-date-listing footer,
		.featured-event-date-listing header {
			-ms-flex-negative: 0;
			flex-shrink: 0;
			width: 100%
		}
	}

	.featured-event-date-listing header h2 {
		margin-bottom: 4rem
	}

	.featured-event-date-listing .front-featured-dates {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		padding-bottom: 4rem
	}

	@media screen and (min-width:64em) {
		.featured-event-date-listing .front-featured-dates {
			width: calc(33.33333% - 2rem);
			padding-right: 2rem
		}
	}

	.featured-event-date-listing .front-featured-dates .article-listing__title h2 {
		font-size: 1.6rem;
		font-weight: 700;
		line-height: 1.63;
		letter-spacing: -.013rem
	}

	.featured-event-date-listing .front-featured-dates .article-listing__title h2:before {
		content: none
	}

	.featured-event-date-listing .front-featured-dates .article-listing__title h2 a {
		color: #4d4d4c
	}

	.featured-event-date-listing footer {
		text-align: right
	}

	#block-views-block-events-listing-block-3,
	.block-views-blocknews-block-2 {
		position: relative
	}

	.front-featured-events--content,
	.front-featured-news--content {
		position: relative;
		padding-top: 11rem;
		padding-right: 4rem
	}

	@media screen and (min-width:46.875em) {

		.front-featured-events--content,
		.front-featured-news--content {
			padding-top: 4rem
		}
	}

	.front-featured-events--content img,
	.front-featured-news--content img {
		position: absolute;
		z-index: -1;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		max-height: 30rem;
		-o-object-fit: cover;
		object-fit: cover
	}

	@media screen and (min-width:46.875em) {

		.front-featured-events--content img,
		.front-featured-news--content img {
			max-height: 100%
		}
	}

	.front-featured-events--content {
		padding-right: 4rem
	}

	@media screen and (min-width:46.875em) {
		.front-featured-events--content {
			padding-right: 0
		}
	}

	@media screen and (min-width:53.75em) {
		.front-featured-events--content {
			padding-top: 8rem
		}
	}

	@media screen and (min-width:46.875em) {

		.front-featured-events--content .front-featured-events,
		.front-featured-events--content footer,
		.front-featured-events--content header {
			max-width: 50rem;
			padding-left: 2rem
		}
	}

	@media screen and (min-width:53.75em) {

		.front-featured-events--content .front-featured-events,
		.front-featured-events--content footer,
		.front-featured-events--content header {
			width: 50%;
			padding-right: 4rem
		}
	}

	@media screen and (min-width:75em) {

		.front-featured-events--content .front-featured-events,
		.front-featured-events--content footer,
		.front-featured-events--content header {
			padding-left: 4rem
		}
	}

	@media screen and (min-width:53.75em) {
		.front-featured-events--content header {
			padding-bottom: 2rem;
			padding-left: 4rem
		}
	}

	.front-featured-events--content h2 {
		margin-bottom: 0;
		padding-top: 4rem;
		padding-left: 4rem;
		background: #fff
	}

	.front-featured-events--content h2:before {
		top: 4.6rem;
		left: 2.2rem
	}

	@media screen and (min-width:40em) {
		.front-featured-events--content h2 {
			padding-left: 4rem
		}
	}

	@media screen and (min-width:53.75em) {
		.front-featured-events--content h2 {
			margin-bottom: 3rem;
			padding: 0;
			background: none
		}

		.front-featured-events--content h2:before {
			top: .6rem;
			left: -1.8rem
		}
	}

	@media screen and (min-width:64em) {
		.front-featured-events--content h2 {
			font-size: 3rem
		}

		.front-featured-events--content h2:before {
			top: .8rem
		}
	}

	@media screen and (min-width:75em) {
		.front-featured-events--content h2:before {
			left: -3.8rem
		}
	}

	@media screen and (min-width:46.875em) {
		.front-featured-events--content img {
			left: 40%;
			max-width: 60%
		}
	}

	@media screen and (min-width:53.75em) {
		.front-featured-events--content img {
			left: 50%;
			max-width: 50%
		}
	}

	@media screen and (min-width:67.5em) {
		.front-featured-events--content img {
			width: auto;
			max-width: 73rem
		}
	}

	.front-featured-events--content .front-featured-events {
		padding: 4rem;
		background: #fff
	}

	.front-featured-events--content .front-featured-events+.front-featured-events {
		margin-top: -4rem
	}

	@media screen and (min-width:53.75em) {
		.front-featured-events--content .front-featured-events {
			padding: 0 4rem
		}

		.front-featured-events--content .front-featured-events+.front-featured-events {
			margin-top: 4rem
		}
	}

	.front-featured-events--content .front-featured-events .feat-event__content .date-recur-date,
	.front-featured-events--content .front-featured-events .feat-event__content p {
		font-size: 1.4rem;
		font-weight: 400;
		line-height: 1.57;
		margin-bottom: 1rem;
		letter-spacing: -.018rem;
		color: #71716f
	}

	.front-featured-events--content .front-featured-events .feat-event__content .feat-event__content-wrapper {
		margin-left: 3rem
	}

	.front-featured-events--content .front-featured-events .feat-event__content .feat-event__content-wrapper h3 {
		font-size: 1.8rem
	}

	.front-featured-events--content .front-featured-events .feat-event__content .feat-event__content-wrapper .date-recur-date {
		font-size: 1.6rem
	}

	.front-featured-events--content footer {
		padding-right: 4rem;
		padding-bottom: 4rem;
		padding-left: 4rem;
		text-align: right;
		background-color: #fff
	}

	@media screen and (min-width:53.75em) {
		.front-featured-events--content footer {
			margin-top: 4rem;
			padding-bottom: 8rem;
			padding-left: 0
		}
	}

	@media screen and (min-width:75em) {
		.front-featured-events--content footer {
			margin-top: 4rem;
			margin-right: 6rem;
			padding-bottom: 8rem
		}
	}

	@media screen and (min-width:46.875em) {
		.front-featured-news--content img {
			right: 55%;
			left: auto;
			max-width: 45%
		}
	}

	@media screen and (min-width:64em) {
		.front-featured-news--content img {
			right: 50%;
			left: auto;
			max-width: 50%
		}
	}

	@media screen and (min-width:67.5em) {
		.front-featured-news--content img {
			width: auto;
			max-width: 73rem
		}
	}

	.front .block-views-blocknews-block-2 .container .article-listings {
		padding-top: 2rem;
		padding-bottom: 2rem;
		padding-left: 2rem
	}

	@media screen and (min-width:30em) {
		.front .block-views-blocknews-block-2 .container .article-listings {
			padding-right: 4rem;
			padding-left: 4rem
		}
	}

	@media screen and (min-width:46.875em) {
		.front .block-views-blocknews-block-2 .container .article-listings {
			padding-right: 0;
			padding-left: 0
		}
	}

	.front .block-views-blocknews-block-2 {
		position: relative
	}

	.front .block-views-blocknews-block-2 div.container {
		padding-bottom: 0
	}

	.front .block-views-blocknews-block-2 div.container .views-row {
		background: #fff
	}

	.front .block-views-blocknews-block-2 div.container .views-row h2 {
		font-size: 1.8rem;
		margin-bottom: 0;
		padding: 0
	}

	@media screen and (min-width:46.875em) {
		.front .block-views-blocknews-block-2 div.container {
			padding: 4rem 0 0
		}

		.front .block-views-blocknews-block-2 div.container .views-row,
		.front .block-views-blocknews-block-2 div.container footer,
		.front .block-views-blocknews-block-2 div.container header {
			margin-left: 45%;
			padding-right: 4rem;
			padding-left: 4rem
		}

		.front .block-views-blocknews-block-2 div.container header {
			padding-top: 4rem;
			padding-bottom: 3rem;
			padding-left: 6rem;
			background-color: #fff
		}

		.front .block-views-blocknews-block-2 div.container header h2 {
			margin-bottom: 0
		}
	}

	@media screen and (min-width:53.75em) {
		.front .block-views-blocknews-block-2 div.container header {
			padding-left: 4rem
		}
	}

	@media screen and (min-width:64em) {

		.front .block-views-blocknews-block-2 div.container .views-row,
		.front .block-views-blocknews-block-2 div.container footer,
		.front .block-views-blocknews-block-2 div.container header {
			max-width: 50rem;
			margin-left: 50%;
			padding-left: 6rem
		}
	}

	@media screen and (min-width:75em) {
		.front .block-views-blocknews-block-2 div.container {
			padding-left: 8rem
		}
	}

	.front .block-views-blocknews-block-2 div.container h2 {
		margin-bottom: 0;
		padding: 4rem 2rem 2rem 4rem;
		background: #fff
	}

	.front .block-views-blocknews-block-2 div.container h2:before {
		top: 4.6rem;
		left: 2.2rem
	}

	@media screen and (min-width:46.875em) {
		.front .block-views-blocknews-block-2 div.container h2 {
			margin-bottom: 3rem;
			padding: 0;
			background: none
		}

		.front .block-views-blocknews-block-2 div.container h2:before {
			top: .6rem;
			left: -1.8rem
		}
	}

	@media screen and (min-width:64em) {
		.front .block-views-blocknews-block-2 div.container h2 {
			font-size: 3rem
		}

		.front .block-views-blocknews-block-2 div.container h2:before {
			top: .8rem
		}
	}

	@media screen and (min-width:75em) {
		.front .block-views-blocknews-block-2 div.container h2:before {
			left: -3.8rem
		}
	}

	.front .block-views-blocknews-block-2 footer {
		padding-top: 2rem;
		padding-bottom: 8rem;
		text-align: right;
		background: #fff
	}

	@media screen and (min-width:46.875em) {
		.front .block-views-blocknews-block-2 footer {
			margin-bottom: 0
		}
	}

	@media screen and (min-width:75em) {
		.front .block-views-blocknews-block-2 footer {
			margin-top: 4rem
		}
	}

	.wf-loading {
		opacity: 0
	}

	.wf-active,
	.wf-inactive {
		opacity: 1
	}

	.container {
		position: relative;
		width: 114rem;
		max-width: 100%;
		margin-right: auto;
		margin-left: auto
	}

	@media screen and (min-width:64em) {

		.footer__main>.container,
		header>.container {
			padding-right: 4rem;
			padding-left: 4rem
		}
	}

	.container-menu {
		position: relative;
		overflow: hidden;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		width: 114rem;
		max-width: 100%;
		margin-right: auto;
		margin-left: auto
	}

	@media screen and (min-width:64em) {
		.container-menu {
			padding-right: 4rem
		}
	}

	.container-menu .container {
		padding-right: 0;
		padding-left: 0
	}

	#main {
		position: relative;
		overflow: hidden
	}

	@media screen and (min-width:64em) {

		.has-sidebar,
		.no-sidebar {
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex;
			-webkit-box-align: start;
			-ms-flex-align: start;
			align-items: flex-start;
			width: 114rem;
			max-width: 100%;
			margin-right: auto;
			margin-left: auto
		}
	}

	.main_content {
		position: relative;
		z-index: 1;
		width: 114rem;
		max-width: 100%;
		margin-right: auto;
		margin-left: auto;
		padding-top: 4rem;
		padding-right: 4rem;
		padding-left: 4rem
	}

	.has-hero .main_content {
		margin-top: -4rem
	}

	.has-hero .main_content:before {
		position: absolute;
		z-index: -1;
		top: 0;
		left: 0;
		width: calc(100% - 4rem);
		height: 20rem;
		content: "";
		background-color: #fff
	}

	@media screen and (min-width:75em) {
		.has-hero .main_content:before {
			left: -4rem;
			width: 100%
		}
	}

	@media screen and (min-width:81.25em) {
		.has-hero .main_content:before {
			width: 1000%
		}
	}

	@media screen and (min-width:64em) {
		.has-hero .main_content {
			margin-top: -15rem
		}
	}

	.has-hero .content-header {
		padding-top: 0
	}

	@media screen and (min-width:64em) {
		.has-hero .content-header {
			padding-top: 0;
			padding-right: 4rem
		}

		.has-hero .content-header nav {
			margin-bottom: 4rem
		}
	}

	@media screen and (min-width:75em) {
		.has-hero .content-header {
			padding-top: 4rem
		}
	}

	.has-sidebar.has-hero .main_content {
		position: relative
	}

	@media screen and (min-width:64em) {
		.has-sidebar.has-hero .main_content:before {
			width: calc(100% + 16rem)
		}
	}

	@media screen and (min-width:75em) {
		.has-sidebar.has-hero .main_content:before {
			left: -4rem;
			width: calc(100% + 20rem)
		}
	}

	@media screen and (min-width:64em) {
		.has-sidebar .main_content {
			width: auto;
			min-width: 0;
			margin-right: 0;
			margin-left: 0
		}

		.has-sidebar .content-header {
			padding-right: 0
		}
	}

	.front .main_content {
		padding-top: 0
	}

	.front .main_content:before {
		top: -2.5rem;
		left: 4rem;
		width: calc(100% - 8rem)
	}

	@media screen and (min-width:64em) {
		.front .main_content:before {
			left: 4rem;
			width: calc(100% - 4rem)
		}
	}

	@media screen and (min-width:75em) {
		.front .main_content:before {
			width: 1000%
		}
	}

	@media screen and (min-width:64em) and (max-width:74.9375em) {
		.front .main_content {
			padding-top: 1rem
		}
	}

	@media screen and (min-width:64em) {
		.front .content-header {
			margin-right: auto;
			margin-left: 8rem
		}
	}

	@media screen and (min-width:75em) {
		.front .content-header nav {
			margin-bottom: 4rem
		}
	}

	@media screen and (min-width:75em) {
		.page-node-type-landing-page .content-area {
			padding-top: 4rem
		}
	}

	.page-user-login .content-area {
		padding-bottom: 4rem
	}

	@media screen and (min-width:64em) {
		.page-user-login .content-area {
			padding-bottom: 8rem
		}
	}

	.main_menu__wrapper .header__logo {
		margin-top: .6rem;
		margin-bottom: .6rem
	}

	a.site_logo__link {
		display: block
	}

	.site_logo__image {
		display: block;
		width: 34rem;
		max-width: 34rem;
		height: 6.7rem;
		max-height: 6.7rem
	}

	.footer__main {
		font-weight: 300;
		border-top: 1.3rem solid #93bf37;
		background: #fff
	}

	.footer__primary {
		padding: 3rem 4rem
	}

	.footer__primary p {
		margin-bottom: 0
	}

	.footer__primary hr {
		margin-top: 3rem;
		margin-bottom: 3rem
	}

	@media screen and (min-width:46.875em) {
		.footer__primary {
			padding-top: 6rem;
			padding-bottom: 6rem
		}

		.footer__primary hr {
			margin-top: 6rem;
			margin-bottom: 6rem
		}
	}

	@media screen and (min-width:40em) {

		.footer__primary_lower,
		.footer__primary_upper {
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex;
			-ms-flex-wrap: wrap;
			flex-wrap: wrap;
			margin-right: -2rem;
			margin-left: -2rem
		}
	}

	@media screen and (min-width:46.875em) {

		.footer__primary_lower,
		.footer__primary_upper {
			-ms-flex-wrap: nowrap;
			flex-wrap: nowrap
		}
	}

	@media screen and (min-width:64em) {

		.footer__primary_lower,
		.footer__primary_upper {
			margin-right: auto;
			margin-left: auto
		}

		.footer__primary_lower div:first-child,
		.footer__primary_upper div:first-child {
			padding-left: 0
		}

		.footer__primary_lower div:last-child,
		.footer__primary_upper div:last-child {
			padding-right: 0
		}
	}

	@media screen and (min-width:40em) {

		.footer__contact,
		.footer__description,
		.footer__location,
		.footer__logo {
			padding-right: 2rem;
			padding-left: 2rem
		}
	}

	@media screen and (min-width:40em) {
		.footer__description {
			-ms-flex-preferred-size: 60%;
			flex-basis: 60%
		}
	}

	@media screen and (min-width:46.875em) {
		.footer__description {
			-ms-flex-preferred-size: 70%;
			flex-basis: 70%
		}
	}

	@media screen and (min-width:75em) {
		.footer__description {
			-ms-flex-preferred-size: 61.66666%;
			flex-basis: 61.66666%
		}
	}

	.footer__logo {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		margin-top: 3rem
	}

	@media screen and (min-width:40em) {
		.footer__logo {
			-ms-flex-preferred-size: 40%;
			flex-basis: 40%;
			margin-top: 0
		}
	}

	@media screen and (min-width:46.875em) {
		.footer__logo {
			-ms-flex-preferred-size: 30%;
			flex-basis: 30%
		}
	}

	@media screen and (min-width:75em) {
		.footer__logo {
			margin-left: 8.333333%
		}
	}

	.footer__logo__image {
		width: 14rem;
		max-width: 14rem;
		height: 5.4rem;
		max-height: 5.4rem
	}

	@media screen and (min-width:40em) {
		.footer__contact {
			-webkit-box-flex: 1;
			-ms-flex: 1;
			flex: 1;
			-ms-flex-preferred-size: 100%;
			flex-basis: 100%
		}
	}

	@media screen and (min-width:46.875em) {
		.footer__contact {
			-ms-flex-preferred-size: 40%;
			flex-basis: 40%
		}
	}

	.footer__location {
		margin-top: 3rem
	}

	@media screen and (min-width:40em) {
		.footer__location {
			-webkit-box-flex: 1;
			-ms-flex: 1;
			flex: 1;
			-ms-flex-preferred-size: 50%;
			flex-basis: 50%
		}
	}

	@media screen and (min-width:46.875em) {
		.footer__location {
			-ms-flex-preferred-size: 30%;
			flex-basis: 30%;
			margin-top: 0
		}
	}

	.location__title {
		display: block;
		color: #5a7826
	}

	.menu-footer-social-links__item,
	.primary-menu-footer-social-links {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex
	}

	.menu-footer-social-links__item {
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center
	}

	.menu-footer-social-links__item:first-of-type {
		margin-left: -1rem
	}

	.menu-footer-social-links__link {
		padding: 1rem
	}

	.menu-footer-social-links__link:not(.social_link) {
		font-size: 1.2rem;
		font-weight: 400;
		line-height: 1;
		margin-bottom: -.4rem
	}

	.social_link {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
		max-width: 3.8rem;
		max-height: 3.8rem
	}

	.social_link .icon {
		width: 1.8rem;
		max-width: 1.8rem;
		height: 1.8rem;
		max-height: 1.8rem;
		-webkit-transition-duration: .3s;
		transition-duration: .3s;
		-webkit-transition-property: fill;
		transition-property: fill;
		fill: #71716f
	}

	.social_link[href*=facebook]:hover .icon {
		fill: #3c5a99
	}

	.social_link[href*=twitter]:hover .icon {
		fill: #00aced
	}

	.social_link[href*=instagram]:hover .icon {
		fill: #c13584
	}

	.social_link[href*=linkedin]:hover .icon {
		fill: #2867b2
	}

	.social_link[href*=youtube]:hover .icon {
		fill: red
	}

	.footer__secondary {
		font-size: 1.4rem;
		line-height: 1;
		padding-top: 2rem;
		padding-bottom: 4rem;
		background: #4d4d4c
	}

	@media screen and (min-width:35em) {
		.footer__secondary {
			padding-top: .5rem;
			padding-bottom: 2rem
		}
	}

	.footer__secondary .container {
		padding-right: 4rem;
		padding-left: 4rem
	}

	.footer__secondary,
	.footer__secondary a {
		font-weight: 400;
		color: #fff
	}

	.copyright+li,
	.footer__menu+li,
	.footer__menu ul li+li {
		margin-top: 1rem
	}

	@media screen and (min-width:30em) {

		.copyright,
		.footer__menu,
		.footer__menu ul li {
			line-height: 1;
			display: inline-block
		}

		.copyright+li,
		.footer__menu+li,
		.footer__menu ul li+li {
			margin-top: 0
		}
	}

	.copyright {
		font-size: 1.4rem;
		font-weight: 400;
		line-height: 1.2;
		margin-top: 2rem;
		margin-right: 2rem;
		margin-bottom: 0
	}

	@media screen and (min-width:64em) {
		.copyright {
			padding-left: 0
		}
	}

	.footer__menu {
		margin-top: 2rem
	}

	.footer__menu ul li {
		margin-right: 1rem
	}

	@media screen and (min-width:64em) {
		.footer__menu {
			padding-right: 0;
			padding-left: 0
		}
	}

	a.menu-footer__link {
		color: #fff
	}

	.sidebar-right a.menu-main__link.is-active {
		background-color: #dfebc7;
		color: #5a7826;
		font-weight: 700
	}

	.sidebar-right a.menu-main__link.is-active:before {
		display: none
	}

	.sidebar-right ul.subnav-main__submenu>li.menu-main__item--active-trail>ul.subnav-main__submenu>ul.subnav-main__submenu {
		padding-left: 2rem
	}

	@media screen and (min-width:64em) {
		.sidebar-right {
			z-index: 1;
			min-width: 35rem;
			margin-top: 4rem;
			margin-bottom: 4rem;
			padding-bottom: 4rem;
			background-color: #f5fcef
		}

		.sidebar-right .primary-menu-main {
			padding-top: 2rem;
			background-color: #f5fcef
		}

		.sidebar-right .menu-main__item {
			line-height: 1.4
		}

		.sidebar-right a.menu-main__link {
			position: relative;
			display: block;
			padding: 3rem 4rem 2.5rem;
			color: #71716f;
			font-size: 1.6rem
		}

		.sidebar-right a.menu-main__link:before {
			content: "";
			position: absolute;
			bottom: -.1rem;
			left: 4rem;
			width: calc(100% - 8rem);
			height: .1rem;
			background-color: rgba(147, 191, 55, .25)
		}

		.sidebar-right a.menu-main__link:after {
			display: none
		}
	}

	@media screen and (min-width:75em) {
		.sidebar-right {
			margin-bottom: 8rem
		}
	}

	.sidebar.sidebar-right .subnav-main__submenu a.menu-main__link {
		padding-left: 6rem
	}

	.sidebar.sidebar-right .subnav-main__submenu .subnav-main__submenu a.menu-main__link {
		padding-left: 8rem
	}

	.sidebar.sidebar-right .subnav-main__submenu .subnav-main__submenu .subnav-main__submenu a.menu-main__link {
		padding-left: 10rem
	}

	.limit-sidebar li:not(.menu-main__item--active-trail) {
		display: none
	}

	.limit-sidebar li.menu-main__item--active-trail li {
		display: list-item
	}

	@media screen and (min-width:64em) {
		.has-hero .sidebar-right {
			margin-top: -11rem
		}
	}

	@media screen and (min-width:75em) {
		.has-hero .sidebar-right {
			margin-top: -7rem
		}
	}

	.menu-utility-navigation__link,
	a.menu-main__link {
		position: relative;
		display: inline-block;
		padding: 1.25rem;
		color: #5a7826
	}

	.menu-utility-navigation__link:after,
	a.menu-main__link:after {
		position: absolute;
		width: 0;
		height: .1rem;
		content: "";
		-webkit-transition: width .3s ease;
		transition: width .3s ease
	}

	.menu-utility-navigation__link.is-active,
	.menu-utility-navigation__link:active,
	.menu-utility-navigation__link:hover,
	.menu-utility-navigation__link:visited,
	a.menu-main__link.is-active,
	a.menu-main__link:active,
	a.menu-main__link:hover,
	a.menu-main__link:visited {
		text-decoration: none
	}

	.menu-utility-navigation__link.is-active:after,
	.menu-utility-navigation__link:active:after,
	.menu-utility-navigation__link:hover:after,
	.menu-utility-navigation__link:visited:after,
	a.menu-main__link.is-active:after,
	a.menu-main__link:active:after,
	a.menu-main__link:hover:after,
	a.menu-main__link:visited:after {
		width: calc(100% - 2.5rem)
	}

	@media (-ms-high-contrast:active),
	(-ms-high-contrast:none) {

		.menu-utility-navigation__link.is-active:after,
		.menu-utility-navigation__link:active:after,
		.menu-utility-navigation__link:hover:after,
		.menu-utility-navigation__link:visited:after,
		a.menu-main__link.is-active:after,
		a.menu-main__link:active:after,
		a.menu-main__link:hover:after,
		a.menu-main__link:visited:after {
			width: 100%;
			max-width: calc(100% - 2.5rem)
		}
	}

	.menu-main__item {
		font-size: 1.4rem;
		line-height: 1
	}

	a.menu-main__link {
		padding: 1.27rem 1.25rem 1.23rem;
		color: #5a7826
	}

	a.menu-main__link:after {
		bottom: 1rem;
		left: 1.25rem;
		background-color: #5a7826
	}

	a.menu-main__link.is-active:after,
	a.menu-main__link:active:after,
	a.menu-main__link:hover:after,
	a.menu-main__link:visited:after {
		width: calc(100% - 2.5rem)
	}

	@media (-ms-high-contrast:active),
	(-ms-high-contrast:none) {

		a.menu-main__link.is-active:after,
		a.menu-main__link:active:after,
		a.menu-main__link:hover:after,
		a.menu-main__link:visited:after {
			width: 100%;
			max-width: calc(100% - 2.5rem)
		}
	}

	.menu-utility-navigation__item {
		font-size: 1.2rem;
		line-height: 1
	}

	.menu-utility-navigation__link {
		padding: 1rem;
		color: #757575;
		border-bottom: none
	}

	.menu-utility-navigation__link:after {
		bottom: .75rem;
		left: 1rem;
		background-color: #757575
	}

	.menu-utility-navigation__link.is-active,
	.menu-utility-navigation__link:active,
	.menu-utility-navigation__link:hover,
	.menu-utility-navigation__link:visited {
		color: #757575
	}

	.menu-utility-navigation__link.is-active:after,
	.menu-utility-navigation__link:active:after,
	.menu-utility-navigation__link:hover:after,
	.menu-utility-navigation__link:visited:after {
		width: calc(100% - 2rem)
	}

	@media (-ms-high-contrast:active),
	(-ms-high-contrast:none) {

		.menu-utility-navigation__link.is-active:after,
		.menu-utility-navigation__link:active:after,
		.menu-utility-navigation__link:hover:after,
		.menu-utility-navigation__link:visited:after {
			width: 100%;
			max-width: calc(100% - 2rem)
		}
	}

	.menu__search {
		position: relative;
		display: -webkit-inline-box;
		display: -ms-inline-flexbox;
		display: inline-flex;
		height: 4rem;
		margin-top: 0
	}

	.menu__search_input {
		-webkit-transition: border-color .3s ease, -webkit-box-shadow .3s ease;
		transition: border-color .3s ease, -webkit-box-shadow .3s ease;
		transition: border-color .3s ease, box-shadow .3s ease;
		transition: border-color .3s ease, box-shadow .3s ease, -webkit-box-shadow .3s ease;
		color: #b7b7b7;
		border: .1rem solid transparent;
		background-color: #f6f6f6;
		-webkit-box-shadow: .4rem .6rem 1rem 0 rgba(0, 0, 0, .11);
		box-shadow: .4rem .6rem 1rem 0 rgba(0, 0, 0, .11)
	}

	.menu__search_input::-webkit-input-placeholder {
		color: #b7b7b7
	}

	.menu__search_input::-moz-placeholder {
		color: #b7b7b7
	}

	.menu__search_input:-ms-input-placeholder {
		color: #b7b7b7
	}

	.menu__search_input::-ms-input-placeholder {
		color: #b7b7b7
	}

	.menu__search_input::placeholder {
		color: #b7b7b7
	}

	.menu__search_input:focus {
		border-color: #ccd8c7;
		outline: none;
		-webkit-box-shadow: .4rem .6rem 1rem 0 rgba(204, 216, 199, .89);
		box-shadow: .4rem .6rem 1rem 0 rgba(204, 216, 199, .89)
	}

	.menu__search_submit {
		position: absolute;
		z-index: 999;
		top: .1rem;
		right: .1rem;
		width: 4.2rem;
		height: calc(100% - .2rem);
		cursor: pointer;
		opacity: 0
	}

	.menu__search_submit:hover+.menu__search_icon {
		fill: #93bf37
	}

	input[type=submit].menu__search_submit {
		padding: 0
	}

	.menu__search_icon {
		position: absolute;
		top: calc(50% - 1rem);
		right: 1.1rem;
		width: 1.9rem;
		height: 1.9rem;
		-webkit-transition: fill .3s ease;
		transition: fill .3s ease;
		pointer-events: none;
		fill: #5a7826
	}

	@media screen and (min-width:64em) {

		.utility_nav,
		.utility_nav__wrapper {
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex
		}

		.utility_nav {
			-webkit-box-align: center;
			-ms-flex-align: center;
			align-items: center;
			margin-left: auto;
			border-bottom: .1rem solid #ccd8c7
		}

		.secondary_menu {
			display: inline-block
		}

		.primary-menu-utility-navigation {
			margin-top: 2rem
		}

		.main_menu__wrapper,
		.primary-menu-utility-navigation {
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex;
			margin-bottom: 2rem
		}

		.main_menu {
			margin-right: 8rem;
			margin-left: auto
		}

		.main_menu .primary-menu-main {
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex;
			margin-top: 1.75rem;
			margin-bottom: 1.75rem
		}
	}

	@media screen and (min-width:64em) {
		.main_menu {
			margin-right: -1.2rem;
			margin-left: auto
		}

		.header__logo {
			padding-left: 3.5rem
		}
	}

	.role--authenticated .mobile_nav__wrapper {
		top: 12.9rem;
		height: calc(100vh - 12.9rem)
	}

	.mobile_nav__wrapper {
		position: fixed;
		top: 9rem;
		left: calc(-100% - 2rem);
		z-index: 999;
		overflow-x: hidden;
		width: 100vw;
		height: calc(100vh - 9rem);
		border-top: .8rem solid #93bf37;
		background-color: #fff;
		-webkit-transition: left .4s ease;
		transition: left .4s ease;
		-ms-overflow-style: -ms-autohiding-scrollbar
	}

	.mobile_nav__wrapper.open {
		left: 0
	}

	.mobile_nav__wrapper .mobile_menu .menu-main__link {
		margin-right: 4rem;
		margin-left: 4rem
	}

	.mobile_nav__wrapper .menu__search {
		margin-top: 2rem;
		margin-right: 5rem;
		margin-left: 5rem
	}

	.mobile_nav__wrapper .menu-utility-navigation__link {
		margin-right: 4rem;
		margin-left: 4rem
	}

	.mobile_nav__header {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		padding: 2.5rem 4rem 2.5rem 1.2rem
	}

	.mobile_nav__header .site_logo__image {
		width: 20rem;
		max-width: 20rem;
		height: 4rem;
		max-height: 4rem;
		margin-top: 0
	}

	@media screen and (min-width:40em) {
		.mobile_nav__header {
			padding-left: 3.2rem
		}
	}

	.mobile_nav__close {
		margin-left: auto;
		background-color: transparent
	}

	.mobile_nav__button {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-orient: horizontal;
		-webkit-box-direction: normal;
		-ms-flex-direction: row;
		flex-direction: row;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		height: 4rem;
		margin-right: -.8rem;
		padding: .4rem .8rem;
		cursor: pointer;
		-webkit-transition-timing-function: linear;
		transition-timing-function: linear;
		-webkit-transition-duration: .3s;
		transition-duration: .3s;
		-webkit-transition-property: opacity, -webkit-filter;
		transition-property: opacity, -webkit-filter;
		transition-property: opacity, filter;
		transition-property: opacity, filter, -webkit-filter
	}

	.mobile_nav__button,
	.mobile_nav__button:hover {
		background-color: transparent
	}

	.mobile_nav__button:focus {
		outline-color: #ccd8c7
	}

	.mobile_nav__button_wrapper {
		position: relative;
		width: 100%;
		width: 2.4rem;
		height: 2.4rem
	}

	.mobile_nav__button_inner {
		top: calc(50% - .8rem);
		display: block;
		-webkit-transition-timing-function: cubic-bezier(.55, .055, .675, .19);
		transition-timing-function: cubic-bezier(.55, .055, .675, .19);
		-webkit-transition-duration: 75ms;
		transition-duration: 75ms
	}

	.mobile_nav__button_inner,
	.mobile_nav__button_inner:after,
	.mobile_nav__button_inner:before {
		position: absolute;
		width: 2.4rem;
		height: .4rem;
		background-color: #4d4d4c
	}

	.mobile_nav__button_inner:after,
	.mobile_nav__button_inner:before {
		content: "";
		display: block;
		-webkit-transition-timing-function: ease;
		transition-timing-function: ease;
		-webkit-transition-duration: .15s;
		transition-duration: .15s;
		-webkit-transition-property: -webkit-transform;
		transition-property: -webkit-transform;
		transition-property: transform;
		transition-property: transform, -webkit-transform
	}

	.mobile_nav__button_inner:before {
		top: -.8rem;
		-webkit-transition: top 75ms ease .12s, opacity 75ms ease;
		transition: top 75ms ease .12s, opacity 75ms ease
	}

	.mobile_nav__button_inner:after {
		bottom: -.8rem;
		-webkit-transition: bottom 75ms ease .12s, -webkit-transform 75ms cubic-bezier(.55, .055, .675, .19);
		transition: bottom 75ms ease .12s, -webkit-transform 75ms cubic-bezier(.55, .055, .675, .19);
		transition: bottom 75ms ease .12s, transform 75ms cubic-bezier(.55, .055, .675, .19);
		transition: bottom 75ms ease .12s, transform 75ms cubic-bezier(.55, .055, .675, .19), -webkit-transform 75ms cubic-bezier(.55, .055, .675, .19)
	}

	.mobile_nav__button.open .mobile_nav__button_inner {
		-webkit-transition-delay: .12s;
		transition-delay: .12s;
		-webkit-transition-timing-function: cubic-bezier(.215, .61, .355, 1);
		transition-timing-function: cubic-bezier(.215, .61, .355, 1);
		-webkit-transform: rotate(45deg);
		transform: rotate(45deg)
	}

	.mobile_nav__button.open .mobile_nav__button_inner:before {
		top: 0;
		opacity: 0;
		-webkit-transition: top 75ms ease, opacity 75ms ease .12s;
		transition: top 75ms ease, opacity 75ms ease .12s
	}

	.mobile_nav__button.open .mobile_nav__button_inner:after {
		bottom: 0;
		-webkit-transition: bottom 75ms ease, -webkit-transform 75ms cubic-bezier(.215, .61, .355, 1) .12s;
		transition: bottom 75ms ease, -webkit-transform 75ms cubic-bezier(.215, .61, .355, 1) .12s;
		transition: bottom 75ms ease, transform 75ms cubic-bezier(.215, .61, .355, 1) .12s;
		transition: bottom 75ms ease, transform 75ms cubic-bezier(.215, .61, .355, 1) .12s, -webkit-transform 75ms cubic-bezier(.215, .61, .355, 1) .12s;
		-webkit-transform: rotate(-90deg);
		transform: rotate(-90deg)
	}

	.mobile_nav__button_text {
		position: absolute;
		bottom: -.6rem;
		left: 0;
		width: 100%;
		color: #4d4d4c;
		font-size: .75rem
	}

	.mobile_menu {
		margin-top: 4rem
	}

	.mobile_nav__divider {
		margin: 5rem 5rem 4rem;
		background-color: #ccd8c7
	}

	.mobile_secondary {
		margin-bottom: 4rem
	}

	.mobile_tertiary {
		padding-top: 4rem;
		padding-bottom: 2.5rem;
		background-color: #f5fcef
	}

	.mobile_tertiary .primary-menu-main>.menu-main__item:last-of-type a.menu-main__link:before {
		display: none
	}

	.mobile_tertiary a.menu-main__link {
		position: relative;
		display: block;
		padding: 2.5rem 5rem 2.2rem;
		color: #71716f
	}

	.mobile_tertiary a.menu-main__link:before {
		content: "";
		position: absolute;
		bottom: -.1rem;
		left: 5rem;
		width: calc(100% - 10rem);
		height: .1rem;
		background-color: rgba(147, 191, 55, .25)
	}

	.mobile_tertiary a.menu-main__link:after {
		display: none
	}

	.mobile_tertiary a.menu-main__link.is-active {
		background-color: #dfebc7;
		color: #5a7826;
		font-weight: 700
	}

	.mobile_tertiary a.menu-main__link.is-active:before {
		display: none
	}

	.mobile_tertiary .subnav-main__submenu a.menu-main__link {
		padding-right: 7rem;
		padding-left: 7rem
	}

	.hero_image__wrapper {
		width: 146rem;
		max-width: 100%;
		max-height: 50rem;
		margin-right: auto;
		margin-left: auto
	}

	.hero_image__inner {
		position: relative;
		overflow: hidden;
		width: 100%;
		max-height: 20rem;
		margin-right: auto;
		margin-left: 0
	}

	@media screen and (min-width:35em) {
		.hero_image__inner {
			max-height: 30rem
		}
	}

	@media screen and (min-width:64em) {
		.hero_image__inner {
			max-height: 50rem
		}
	}

	.hero_image__inner .icon-bg-angle {
		z-index: 1;
		left: -5rem;
		width: 100%;
		-webkit-transform: rotate(180deg);
		transform: rotate(180deg)
	}

	@media screen and (min-width:64em) {
		.hero_image__inner .icon-bg-angle {
			left: -16rem
		}
	}

	@media screen and (min-width:90em) {
		.hero_image__inner .icon-bg-angle {
			left: -5rem
		}
	}

	.hero_image__inner .hero-image {
		z-index: -1;
		width: 100%;
		height: 100%;
		height: 20rem;
		background-repeat: no-repeat;
		background-position: top;
		background-size: cover
	}

	@media screen and (min-width:35em) {
		.hero_image__inner .hero-image {
			height: 30rem
		}
	}

	@media screen and (min-width:64em) {
		.hero_image__inner .hero-image {
			height: 50rem
		}
	}

	.hero_image__inner .hero-image:before {
		position: absolute;
		top: 0;
		width: 100%;
		height: 1.2rem;
		content: "";
		background-color: #93bf37
	}

	.intro_text {
		margin-bottom: 4rem
	}

	.intro_text:last-child {
		margin-bottom: 0
	}

	.intro_text p {
		font-size: 1.8rem;
		line-height: 1.54;
		max-width: 88rem;
		letter-spacing: -.022rem;
		color: #757575
	}

	.intro_text p:last-of-type {
		margin-bottom: 0
	}

	@media screen and (min-width:46.875em) {
		.intro_text p {
			font-size: 2rem
		}
	}

	@media screen and (min-width:64em) {
		.intro_text p {
			font-size: 2.6rem
		}
	}

	@media screen and (min-width:64em) {
		div+.intro_text {
			margin-top: 3rem
		}
	}

	.page_blocks>div:last-of-type:not(.call_to_action):not(.call_to_action_button):not(:first-of-type) {
		margin-bottom: 0;
		padding-bottom: 4rem
	}

	@media screen and (min-width:64em) {
		.page_blocks>div:last-of-type:not(.call_to_action):not(.call_to_action_button):not(:first-of-type) {
			padding-top: 8rem;
			padding-bottom: 8rem
		}
	}

	.page_blocks>div:last-of-type.call_to_action:not(:first-of-type) {
		padding-bottom: 2rem
	}

	@media screen and (min-width:64em) {
		.page_blocks>div:last-of-type.call_to_action:not(:first-of-type) {
			padding-bottom: 0
		}
	}

	.page_blocks>div:last-of-type.call_to_action_button:not(:first-of-type) {
		padding-bottom: 1rem
	}

	@media screen and (min-width:64em) {
		.page_blocks>div:last-of-type.call_to_action_button:not(:first-of-type) {
			padding-bottom: 5rem
		}
	}

	.page_blocks>div:not(.image_only)+div:not(.paragraph--type--related-information) {
		margin-top: 4rem;
		margin-bottom: 0
	}

	.page_blocks>div:not(.image_only)+div:not(.paragraph--type--related-information).call_to_action_button {
		margin-top: 4rem;
		margin-bottom: 1rem
	}

	@media screen and (min-width:64em) {
		.page_blocks>div:not(.image_only)+div:not(.paragraph--type--related-information) {
			margin-top: 8rem
		}

		.page_blocks>div:not(.image_only)+div:not(.paragraph--type--related-information).call_to_action_button {
			margin-top: 6rem;
			margin-bottom: 0
		}
	}

	.page_blocks>div.call_to_action_button+div {
		margin-top: 1rem
	}

	.page_blocks>div.call_to_action_button+div.call_to_action {
		margin-top: 0
	}

	@media screen and (min-width:64em) {
		.page_blocks>div.call_to_action_button+div {
			margin-top: 5rem
		}
	}

	@media screen and (min-width:90em) {
		.page_blocks>div.call_to_action_button+div {
			margin-top: 9rem
		}
	}

	.page_blocks>div.paragraph--type--related-information+div,
	.page_blocks>div.text_video+div {
		margin-top: 0
	}

	@media screen and (min-width:64em) {

		.page_blocks>div.paragraph--type--related-information+div,
		.page_blocks>div.text_video+div {
			margin-top: 8rem
		}
	}

	.page_blocks>div.image_only+div.image_only,
	.page_blocks>div.image_only+div.image_only:last-of-type:not(.call_to_action):not(.call_to_action_button):not(:first-of-type),
	.page_blocks>div.image_only+div.text_only,
	.page_blocks>div.image_only+div.text_only:last-of-type:not(.call_to_action):not(.call_to_action_button):not(:first-of-type),
	.page_blocks>div.text_only+div.image_only,
	.page_blocks>div.text_only+div.image_only:last-of-type:not(.call_to_action):not(.call_to_action_button):not(:first-of-type),
	.page_blocks>div.text_only+div.text_only,
	.page_blocks>div.text_only+div.text_only:last-of-type:not(.call_to_action):not(.call_to_action_button):not(:first-of-type) {
		padding-top: 0
	}

	.page_blocks>div.image_only+.paragraph--type--related-information,
	.page_blocks>div.text_only+.paragraph--type--related-information {
		margin-top: 4rem
	}

	@media screen and (min-width:64em) {

		.page_blocks>div.image_only+.paragraph--type--related-information,
		.page_blocks>div.text_only+.paragraph--type--related-information {
			margin-top: 8rem
		}
	}

	.page_blocks>div.image_only:last-of-type:first-of-type,
	.page_blocks>div.text_only:last-of-type:first-of-type {
		padding-bottom: 4rem
	}

	@media screen and (min-width:64em) {

		.page_blocks>div.image_only:last-of-type:first-of-type,
		.page_blocks>div.text_only:last-of-type:first-of-type {
			padding-bottom: 8rem
		}
	}

	.page_blocks .text_image.has-caption {
		margin-bottom: 0
	}

	@media screen and (min-width:64em) {
		.has-sidebar .page_blocks .text_image__right .page_block__image {
			right: 0
		}
	}

	@media screen and (min-width:46.875em) {
		.has-sidebar .page_blocks .page_block__image {
			position: absolute;
			bottom: 0;
			max-height: 65rem
		}
	}

	.intro_text+.page_blocks {
		margin-top: 4rem
	}

	@media screen and (min-width:64em) {
		.intro_text+.page_blocks {
			margin-top: 8rem
		}
	}

	.call_to_action {
		position: relative
	}

	@media screen and (min-width:64em) {
		.call_to_action {
			padding-right: 0;
			padding-left: 0
		}
	}

	.call_to_action .page_block__image {
		margin-right: -4rem;
		margin-left: -4rem
	}

	@media screen and (min-width:46.875em) {
		.call_to_action .page_block__image {
			position: absolute;
			left: -4rem;
			width: 70%;
			margin-right: 0;
			margin-left: 0
		}
	}

	@media screen and (min-width:64em) {
		.call_to_action .page_block__image {
			width: calc(80% - 4rem)
		}
	}

	@media screen and (min-width:67.5em) {
		.call_to_action .page_block__image {
			left: -8rem;
			width: calc(80% - 2rem)
		}
	}

	@media screen and (min-width:75em) {
		.call_to_action .page_block__image {
			left: -20rem;
			width: calc(80% + 12rem)
		}
	}

	.call_to_action .page_block__image img {
		width: 100%;
		border-top: .8rem solid #93bf37;
		-o-object-fit: cover;
		object-fit: cover
	}

	.call_to_action .page_block__image .icon-bg-angle {
		right: -.1rem;
		-webkit-transform: rotate(180deg) scaleX(-1);
		transform: rotate(180deg) scaleX(-1)
	}

	.call_to_action h2 {
		font-size: 3.4rem;
		margin-top: 2rem
	}

	.call_to_action h2:before {
		display: none
	}

	@media screen and (min-width:46.875em) {
		.call_to_action h2 {
			font-size: 4.4rem;
			margin-top: 0
		}
	}

	@media screen and (min-width:67.5em) {
		.call_to_action h2 {
			font-size: 5.4rem
		}
	}

	.call_to_action p {
		font-size: 1.8rem;
		font-weight: 300;
		margin-bottom: 3rem
	}

	@media screen and (min-width:64em) {
		.call_to_action p {
			font-size: 2.2rem;
			margin-left: auto
		}
	}

	.call_to_action .page_block__text {
		max-width: 64rem;
		padding-bottom: 1rem
	}

	@media screen and (min-width:46.875em) {
		.call_to_action .page_block__text {
			width: calc(60% - 4rem);
			max-width: 40rem;
			margin-left: auto;
			padding-top: 4rem;
			padding-bottom: 3rem;
			text-align: right
		}
	}

	@media screen and (min-width:64em) {
		.call_to_action .page_block__text {
			width: calc(50% - 4rem);
			padding-top: 8rem;
			padding-bottom: 6rem
		}
	}

	.page_blocks .call_to_action_button {
		margin-bottom: 3rem
	}

	@media screen and (min-width:64em) {
		.page_blocks .call_to_action_button {
			margin-bottom: 5rem
		}
	}

	@media screen and (min-width:46.875em) {
		.has-sidebar .main_content .call_to_action .page_block__image {
			position: absolute;
			bottom: 0;
			max-height: 65rem
		}
	}

	@media screen and (min-width:46.875em) {
		.has-sidebar .main_content .call_to_action .page_block__text {
			max-width: 32rem
		}
	}

	@media screen and (min-width:64em) {
		.has-sidebar .main_content .call_to_action .page_block__text {
			margin-right: -4rem
		}
	}

	@media screen and (min-width:46.875em) {
		.has-sidebar .call_to_action .page_block__image {
			width: 80%
		}
	}

	@media screen and (min-width:64em) {
		.has-sidebar .call_to_action .page_block__image {
			width: calc(90% - 6rem)
		}
	}

	@media screen and (min-width:67.5em) {
		.has-sidebar .call_to_action .page_block__image {
			width: calc(90% - 4rem)
		}
	}

	@media screen and (min-width:75em) {
		.has-sidebar .call_to_action .page_block__image {
			width: calc(90% + 10rem)
		}
	}

	@media screen and (min-width:46.875em) {
		.has-sidebar .call_to_action .page_block__text {
			width: 50%
		}
	}

	.cta_button {
		margin-bottom: 3rem;
		padding: 2.5rem 5rem 2rem
	}

	.content-area .page_blocks .text_only ol:last-child,
	.content-area .page_blocks .text_only p:last-child,
	.content-area .page_blocks .text_only ul:not(.tab-list):not(.landing__link_list):not(.search-result):not(.quicktabs-tabs):not(.pager__items):last-child {
		margin-bottom: 0
	}

	.image_only {
		padding-bottom: 4rem
	}

	.image_only img {
		width: 64rem
	}

	@media screen and (min-width:64em) {
		.image_only {
			padding-bottom: 8rem
		}
	}

	.text_image {
		position: relative;
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-orient: vertical;
		-webkit-box-direction: normal;
		-ms-flex-direction: column;
		flex-direction: column;
		width: 100%;
		height: 100%;
		margin-bottom: 4rem
	}

	@media screen and (min-width:46.875em) {
		.text_image {
			-webkit-box-align: center;
			-ms-flex-align: center;
			align-items: center;
			-webkit-box-orient: horizontal;
			-webkit-box-direction: normal;
			-ms-flex-direction: row;
			flex-direction: row;
			margin-bottom: 0;
			padding-top: 4rem;
			padding-bottom: 4rem
		}
	}

	@media screen and (min-width:64em) {
		.text_image {
			margin-top: 0;
			margin-bottom: 0;
			padding-top: 8rem;
			padding-bottom: 8rem
		}
	}

	@media screen and (min-width:46.875em) {
		.text_image.has-caption {
			margin-bottom: 4rem
		}

		.text_image.has-caption .page_block__image,
		.text_image.has-caption .page_block__text {
			padding-bottom: 3.6rem
		}
	}

	@media screen and (min-width:64em) {
		.text_image.has-caption {
			padding-bottom: 6rem
		}
	}

	.text_image p:last-of-type {
		margin-bottom: 0
	}

	.text_image .page_block__text {
		-ms-flex-negative: 0;
		flex-shrink: 0;
		max-width: 64rem
	}

	@media screen and (min-width:46.875em) {
		.text_image .page_block__text {
			width: calc(50% - 4rem)
		}
	}

	.text_image .page_block__image {
		margin-right: -4rem;
		margin-bottom: 2rem;
		margin-left: -4rem
	}

	@media screen and (max-width:46.8125em) {
		.text_image .page_block__image {
			padding-bottom: 0 !important
		}
	}

	@media screen and (min-width:46.875em) {
		.text_image .page_block__image {
			position: absolute;
			margin-bottom: 0
		}
	}

	.text_image .page_block__image_inner {
		position: relative;
		z-index: -1;
		overflow: hidden;
		width: 100%
	}

	@media screen and (min-width:46.875em) {
		.text_image .page_block__image_inner {
			height: 100%
		}
	}

	.text_image .page_block__image_inner .hero-image,
	.text_image .page_block__image_inner img {
		width: 100%;
		height: 20rem;
		-o-object-fit: cover;
		object-fit: cover
	}

	@media screen and (min-width:35em) {

		.text_image .page_block__image_inner .hero-image,
		.text_image .page_block__image_inner img {
			height: 30rem
		}
	}

	@media screen and (min-width:46.875em) {

		.text_image .page_block__image_inner .hero-image,
		.text_image .page_block__image_inner img {
			height: 100%
		}
	}

	.text_image .icon-bg-angle {
		top: -5%;
		width: 100%;
		height: 110%;
		pointer-events: none
	}

	.text_image .image_caption {
		margin-right: 4rem;
		margin-left: 4rem
	}

	.text_image.text_image__left .icon-bg-angle {
		right: -.1rem;
		-webkit-transform: rotate(180deg) scaleX(-1);
		transform: rotate(180deg) scaleX(-1)
	}

	@media screen and (min-width:46.875em) {
		.text_image.text_image__left .page_block__image {
			margin-right: 40%
		}
	}

	@media screen and (min-width:53.75em) {
		.text_image.text_image__left .page_block__image {
			margin-right: calc(40% + 2rem)
		}
	}

	@media screen and (min-width:64em) {
		.text_image.text_image__left .page_block__image {
			margin-right: calc(50% - 8rem)
		}
	}

	@media screen and (min-width:67.5em) {
		.text_image.text_image__left .page_block__image {
			left: -4rem
		}

		.text_image.text_image__left .page_block__image .image_caption {
			margin-left: 8rem
		}
	}

	@media screen and (min-width:75em) {
		.text_image.text_image__left .page_block__image {
			left: -16rem
		}

		.text_image.text_image__left .page_block__image .image_caption {
			margin-left: 20rem
		}
	}

	@media screen and (min-width:46.875em) {
		.text_image.text_image__left .page_block__text {
			margin-left: calc(50% + 4rem)
		}
	}

	.text_image__right .icon-bg-angle {
		left: -.1rem;
		-webkit-transform: rotate(180deg);
		transform: rotate(180deg)
	}

	.text_image__right .page_block__image {
		-webkit-box-ordinal-group: 2;
		-ms-flex-order: 1;
		order: 1
	}

	@media screen and (min-width:46.875em) {
		.text_image__right .page_block__image {
			-webkit-box-ordinal-group: 3;
			-ms-flex-order: 2;
			order: 2;
			margin-left: calc(40% - 2rem)
		}
	}

	@media screen and (min-width:53.75em) {
		.text_image__right .page_block__image {
			margin-left: calc(40% + 2rem)
		}
	}

	@media screen and (min-width:64em) {
		.text_image__right .page_block__image {
			margin-left: calc(50% - 8rem)
		}
	}

	@media screen and (min-width:67.5em) {
		.text_image__right .page_block__image {
			right: -4rem
		}

		.text_image__right .page_block__image .image_caption {
			margin-right: 8rem
		}
	}

	@media screen and (min-width:75em) {
		.text_image__right .page_block__image {
			right: -16rem
		}

		.text_image__right .page_block__image .image_caption {
			margin-right: 20rem
		}
	}

	.text_image__right .page_block__text {
		-webkit-box-ordinal-group: 3;
		-ms-flex-order: 2;
		order: 2
	}

	@media screen and (min-width:46.875em) {
		.text_image__right .page_block__text {
			-webkit-box-ordinal-group: 2;
			-ms-flex-order: 1;
			order: 1
		}
	}

	@media screen and (min-width:46.875em) {
		.has-sidebar .text_image.text_image__left .page_block__image {
			margin-right: calc(40% + 2rem)
		}
	}

	@media screen and (min-width:64em) {
		.has-sidebar .text_image.text_image__left .page_block__image {
			margin-right: calc(50% - 6rem)
		}
	}

	@media screen and (min-width:67.5em) {
		.has-sidebar .text_image.text_image__left .page_block__image {
			margin-right: calc(50% - 8rem)
		}
	}

	@media screen and (min-width:64em) {
		.has-sidebar .text_image.text_image__right .page_block__text {
			width: calc(60% - 4rem)
		}
	}

	@media screen and (min-width:67.5em) {
		.has-sidebar .text_image.text_image__right .page_block__text {
			width: calc(70% - 4rem)
		}
	}

	@media screen and (min-width:75em) {
		.has-sidebar .text_image.text_image__right .page_block__text {
			width: calc(75% - 4rem)
		}
	}

	@media screen and (min-width:46.875em) {
		.has-sidebar .text_image.text_image__right .page_block__image {
			margin-left: 40%
		}
	}

	@media screen and (min-width:64em) {
		.has-sidebar .text_image.text_image__right .page_block__image {
			right: -35rem;
			margin-left: calc(50% - 4rem)
		}
	}

	@media screen and (min-width:67.5em) {
		.has-sidebar .text_image.text_image__right .page_block__image {
			right: -38rem;
			margin-left: calc(60% - 4rem)
		}
	}

	@media screen and (min-width:75em) {
		.has-sidebar .text_image.text_image__right .page_block__image {
			right: -51rem;
			margin-left: calc(65% - 4rem)
		}
	}

	.text_video {
		position: relative;
		padding-top: 4rem;
		padding-bottom: 4rem
	}

	@media screen and (min-width:64em) {
		.text_video {
			padding-top: 8rem;
			padding-bottom: 8rem
		}
	}

	.text_video:after,
	.text_video:before {
		position: absolute;
		z-index: -2;
		top: 0;
		width: 1000%;
		height: 100%;
		content: ""
	}

	.text_video:before {
		right: -130%;
		background-color: #f5fcef
	}

	.text_video:after {
		left: 200%;
		background-color: #fff
	}

	.text_video div[class^=video-embed] {
		max-width: 64rem;
		margin-bottom: 3rem
	}

	.text_video p+div[class^=video-embed] {
		margin-top: 3rem
	}

	.text_video .icon-bg-angle {
		z-index: -1;
		right: calc(-100% - 4rem);
		width: 1000%
	}

	.paragraph--type--related-information {
		position: relative;
		padding-top: 4rem;
		padding-bottom: 3rem
	}

	@media screen and (min-width:64em) {
		.paragraph--type--related-information {
			padding-top: 8rem;
			padding-bottom: 7rem
		}
	}

	.paragraph--type--related-information:before {
		position: absolute;
		z-index: -2;
		top: 0;
		right: -100%;
		width: 1000%;
		height: 100%;
		content: "";
		background-color: #f5fcef
	}

	.paragraph--type--related-information .landing__link_list {
		margin-bottom: 0
	}

	.paragraph--type--related-information .icon-bg-angle {
		z-index: -1;
		right: calc(-100% - 4rem);
		width: 1000%
	}

	.landing__related .paragraph--type--related-information .icon-bg-angle,
	.landing__related .paragraph--type--related-information:before {
		right: -30%
	}

	@media screen and (max-width:63.9375em) {
		.page-node-type-listing-page .content-area {
			margin-right: -4rem;
			margin-left: -4rem
		}
	}

	.page-node-type-listing-page .page-title {
		font-family: lexia, Helvetica Neue, helvetica, Arial, sans-serif;
		font-size: 3.4rem;
		font-weight: 700;
		line-height: 1.12;
		display: block;
		margin-bottom: 2rem;
		letter-spacing: -.02rem;
		color: #4d4d4c
	}

	@media screen and (min-width:64em) {
		.page-node-type-listing-page .page-title {
			margin-bottom: 4rem
		}
	}

	.page-node-type-listing-page .body-content {
		padding-top: 4rem
	}

	@media screen and (min-width:64em) {
		.page-node-type-listing-page .body-content {
			padding-top: 4rem
		}
	}

	@media screen and (min-width:75em) {
		.page-node-type-listing-page .body-content {
			padding-top: 8rem
		}
	}

	.page-node-type-listing-page:not(.page-registration-records-find-right-funding-your-education):not(.page-student-funding) .body-content {
		padding-bottom: 4rem
	}

	@media screen and (min-width:64em) {
		.page-node-type-listing-page:not(.page-registration-records-find-right-funding-your-education):not(.page-student-funding) .body-content {
			padding-bottom: 5rem
		}
	}

	@media screen and (min-width:75em) {
		.page-node-type-listing-page:not(.page-registration-records-find-right-funding-your-education):not(.page-student-funding) .body-content {
			padding-bottom: 8rem
		}
	}

	.page-node-type-listing-page .body-content .block-views .left,
	.page-node-type-listing-page .quicktabs-tabpage .left {
		margin-bottom: 4rem
	}

	@media screen and (min-width:64em) {

		.page-node-type-listing-page .body-content .block-views .left,
		.page-node-type-listing-page .quicktabs-tabpage .left {
			margin-bottom: 0
		}
	}

	.page-node-type-listing-page .body-content .block-views .icon-bg-angle,
	.page-node-type-listing-page .quicktabs-tabpage .icon-bg-angle {
		z-index: -1;
		right: -50%;
		width: 1000%
	}

	@media screen and (min-width:64em) {

		.page-node-type-listing-page .body-content .block-views .form-submit,
		.page-node-type-listing-page .quicktabs-tabpage .form-submit {
			width: 100%
		}
	}

	.page-node-type-listing-page .body-content .block-views .featured-awards .views-row,
	.page-node-type-listing-page .body-content .block-views .featured-events .views-row,
	.page-node-type-listing-page .body-content .block-views .featured-news .views-row {
		margin-right: 0;
		margin-left: 0;
		padding-right: 0;
		padding-left: 0
	}

	.page-node-type-listing-page .body-content .block-views .featured-awards .views-row:last-of-type,
	.page-node-type-listing-page .body-content .block-views .featured-events .views-row:last-of-type,
	.page-node-type-listing-page .body-content .block-views .featured-news .views-row:last-of-type {
		padding-bottom: 0
	}

	.page-node-type-listing-page .body-content .block-views .actions {
		margin-top: 4rem
	}

	.page-node-type-listing-page .body-content .block-views .form-submit {
		margin-top: 0
	}

	.page-node-type-listing-page .body-content .block-views .form-submit+.form-submit {
		margin-top: 4rem
	}

	.page-node-type-listing-page .body-content .block-views div[class*=cam-] {
		margin-right: 2rem;
		margin-left: 2rem
	}

	@media screen and (min-width:64em) {
		.page-node-type-listing-page .body-content .block-views div[class*=cam-] {
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex;
			margin-right: 0;
			margin-left: 0
		}
	}

	@media screen and (min-width:64em) {
		.page-node-type-listing-page .body-content .block-views .left {
			width: calc(30% + 8rem)
		}
	}

	@media screen and (min-width:64em) {
		.page-node-type-listing-page .body-content .block-views .right {
			width: calc(70% - 12rem);
			margin-left: 4rem
		}
	}

	@media screen and (min-width:64em) {
		.page-node-type-listing-page .body-content .block-views .close-filters {
			padding-left: 4rem
		}
	}

	.page-node-type-listing-page .body-content .block-views .views-row {
		margin-right: -2rem;
		margin-left: -2rem;
		padding: 2rem 4rem
	}

	@media screen and (min-width:64em) {
		.page-node-type-listing-page .body-content .block-views .views-row {
			margin-right: 0;
			margin-left: 0;
			padding-top: 3rem;
			padding-bottom: 3rem
		}
	}

	.page-node-type-listing-page .body-content .block-views .views-row h2 a {
		text-decoration: none
	}

	.page-node-type-listing-page .body-content .block-views .views-row h2 a:active,
	.page-node-type-listing-page .body-content .block-views .views-row h2 a:hover {
		text-decoration: underline
	}

	.page-node-type-listing-page .body-content .block-views .row-content>h2,
	.page-node-type-listing-page .body-content .block-views .row-content>p {
		margin-right: 2rem;
		margin-left: 2rem
	}

	@media screen and (min-width:64em) {

		.page-node-type-listing-page .body-content .block-views .row-content>h2,
		.page-node-type-listing-page .body-content .block-views .row-content>p {
			margin-right: 4rem;
			margin-left: 4rem
		}
	}

	.page-node-type-listing-page .body-content .block-views .row-content>h2 {
		margin-bottom: 2rem
	}

	@media screen and (min-width:64em) {
		.page-node-type-listing-page .body-content .block-views .container {
			padding-right: 0;
			padding-left: 0
		}
	}

	.page-node-type-listing-page .body-content .block-views .pager {
		margin-right: 2rem;
		margin-left: 2rem;
		padding-bottom: 4rem
	}

	@media screen and (min-width:64em) {
		.page-node-type-listing-page .body-content .block-views .pager {
			margin-top: 5rem
		}
	}

	@media screen and (min-width:64em) {
		.page-node-type-listing-page .quicktabs-tabpage .left {
			width: calc(30% - 4rem);
			margin-right: 4rem
		}
	}

	@media screen and (min-width:64em) {
		.page-node-type-listing-page .quicktabs-tabpage .right {
			width: calc(70% - 4rem);
			margin-left: 4rem
		}
	}

	@media screen and (min-width:64em) {
		.page-node-type-listing-page .quicktabs-tabpage div[class*=cam-] {
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex
		}
	}

	.page-node-type-listing-page .views-exposed-form {
		width: 100%;
		padding: 4rem 2rem;
		border-radius: .2rem;
		background-color: #fff;
		-webkit-box-shadow: 0 -.2rem 1.8rem 0 rgba(61, 61, 61, .04);
		box-shadow: 0 -.2rem 1.8rem 0 rgba(61, 61, 61, .04)
	}

	@media screen and (min-width:64em) {
		.page-node-type-listing-page .views-exposed-form {
			padding-right: 4rem;
			padding-left: 4rem
		}
	}

	.page-node-type-listing-page .views-exposed-form>.js-form-item {
		margin-bottom: 4rem
	}

	.page-node-type-listing-page .views-exposed-form div[class$=-course]>span,
	.page-node-type-listing-page .views-exposed-form div[class$=-filter]>span {
		font-weight: 700;
		display: inline-block;
		margin-bottom: 2rem;
		letter-spacing: .06rem;
		text-transform: uppercase;
		color: #5a7826
	}

	.page-node-type-listing-page .exposed-title,
	.page-node-type-listing-page .js-form-item-keys label {
		font-weight: 700;
		margin-bottom: 2rem;
		letter-spacing: .06rem;
		text-transform: uppercase;
		color: #5a7826
	}

	.page-node-type-listing-page .js-form-item-keys .form-text {
		width: 100%;
		padding-top: 1.6rem;
		border: .1rem solid #779f32;
		background-color: #fff;
		-webkit-box-shadow: .4rem .6rem 1rem 0 rgba(0, 0, 0, .11);
		box-shadow: .4rem .6rem 1rem 0 rgba(0, 0, 0, .11)
	}

	.page-node-type-listing-page .date-filter {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap
	}

	.page-node-type-listing-page .date-filter label {
		display: none
	}

	.page-node-type-listing-page .call_to_action {
		padding-right: 4rem;
		padding-bottom: 2rem;
		padding-left: 4rem
	}

	@media screen and (min-width:64em) {
		.page-node-type-listing-page .call_to_action {
			padding-bottom: 0
		}
	}

	.close-filters {
		margin-bottom: 1.4rem
	}

	.close-filters .filters-list {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
		margin-right: -.8rem;
		margin-left: -.8rem
	}

	.close-filters li {
		margin-right: .8rem;
		margin-bottom: 1.6rem;
		margin-left: .8rem
	}

	.close-filters a {
		font-size: 1.6rem;
		position: relative;
		display: inline-block;
		padding: .6rem 1.2rem .3rem 3.4rem;
		-webkit-transition: background-color .3s ease;
		transition: background-color .3s ease;
		color: #fff;
		background-color: #5a7826
	}

	.close-filters a:focus,
	.close-filters a:hover {
		text-decoration: none;
		background-color: #93bf37
	}

	.close-filters span:after,
	.close-filters span:before {
		position: absolute;
		top: .9rem;
		left: 1.6rem;
		width: .4rem;
		height: 1.4rem;
		content: "";
		background-color: #fff
	}

	.close-filters span:before {
		-webkit-transform: rotate(-45deg);
		transform: rotate(-45deg)
	}

	.close-filters span:after {
		-webkit-transform: rotate(45deg);
		transform: rotate(45deg)
	}

	.featured-awards,
	.featured-events,
	.featured-news {
		position: relative;
		margin-right: -2rem;
		margin-bottom: 2rem;
		margin-left: -2rem;
		padding: 4rem
	}

	.featured-awards:empty,
	.featured-events:empty,
	.featured-news:empty {
		display: none
	}

	@media screen and (min-width:64em) {

		.featured-awards,
		.featured-events,
		.featured-news {
			margin-bottom: 1rem;
			padding-right: 6rem;
			padding-bottom: 4rem;
			padding-left: 6rem
		}
	}

	.featured-awards:before,
	.featured-events:before,
	.featured-news:before {
		position: absolute;
		z-index: -1;
		bottom: 0;
		left: 2rem;
		width: calc(100% - 4rem);
		height: 100%;
		content: "";
		background: -webkit-gradient(linear, left top, right bottom, from(transparent), color-stop(50%, transparent), color-stop(50%, #f5fcef), to(#f5fcef));
		background: linear-gradient(to bottom right, transparent 0, transparent 50%, #f5fcef 0, #f5fcef)
	}

	@media screen and (min-width:64em) {

		.featured-awards:before,
		.featured-events:before,
		.featured-news:before {
			top: -11rem;
			right: -18rem;
			left: auto;
			width: calc(100% + 34rem);
			height: calc(100% + 11rem)
		}
	}

	.block-views-blocknews-block-2 .article-listings {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		padding: 4rem
	}

	.block-views-blocknews-block-2 .article-listings h2:before {
		content: none
	}

	.article-listings {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: start;
		-ms-flex-align: start;
		align-items: flex-start
	}

	.article-listings h2 {
		font-size: 1.8rem;
		font-weight: 700;
		line-height: 1.5;
		margin-bottom: 0;
		letter-spacing: -.015rem
	}

	.article-listings h2:before {
		content: none
	}

	.article-listings h2 a {
		color: #4d4d4c
	}

	.article-listing__date {
		font-weight: 300;
		line-height: 1;
		position: relative;
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		margin-right: 2rem;
		padding-right: 2.4rem;
		text-transform: uppercase;
		color: #5a7826
	}

	.article-listing__date .createdDay {
		font-family: lexia, Helvetica Neue, helvetica, Arial, sans-serif;
		font-size: 6rem;
		line-height: 1;
		letter-spacing: -.02rem
	}

	.article-listing__date .createdMonth,
	.article-listing__date .createdYear {
		font-size: 1.2rem;
		font-weight: 700;
		letter-spacing: .045rem
	}

	.article-listing__date .date-wrapper {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-orient: vertical;
		-webkit-box-direction: normal;
		-ms-flex-direction: column;
		flex-direction: column;
		-ms-flex-negative: 0;
		flex-shrink: 0;
		margin-top: -.6rem
	}

	.date-spacer {
		position: absolute;
		right: 0;
		width: .4rem;
		height: 100%;
		border-right: .4rem solid;
		border-bottom: .3rem solid transparent
	}

	.contentbody-content img {
		padding-bottom: 3rem
	}

	.contentbody-content img+.image_caption {
		margin-top: -1.8rem;
		padding-bottom: 2rem
	}

	.contentbody-content .article_meta img {
		padding-bottom: 0
	}

	.profile__info p span:after,
	.profile__titles p span:after {
		content: ",";
		display: inline;
		margin-right: -.1rem;
		margin-left: -.5rem
	}

	.profile__info p span:last-child:after,
	.profile__titles p span:last-child:after {
		content: "";
		display: none
	}

	.profile__titles p {
		margin-bottom: 4rem;
		font-size: 2rem;
		line-height: 1.2;
		letter-spacing: -.025rem
	}

	.profile__titles span+span {
		margin-left: .4rem
	}

	.profile__content {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
		margin-bottom: 2rem
	}

	@media screen and (min-width:35em) {
		.profile__content {
			-ms-flex-wrap: nowrap;
			flex-wrap: nowrap
		}
	}

	.profile__pic {
		-ms-flex-negative: 0;
		flex-shrink: 0;
		max-width: 14rem;
		margin-right: 4rem;
		margin-bottom: 2rem
	}

	.profile__info {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-flex: 1;
		-ms-flex-positive: 1;
		flex-grow: 1;
		-webkit-box-orient: vertical;
		-webkit-box-direction: normal;
		-ms-flex-direction: column;
		flex-direction: column;
		min-width: 0
	}

	.profile__info p {
		margin-bottom: .5rem
	}

	.profile__background {
		margin-top: 2rem
	}

	.profile__background,
	.profile__background p {
		font-size: 1.8rem
	}

	.profile__background p {
		margin-bottom: 2rem
	}

	.profile__links {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		margin-bottom: -2rem;
		padding-top: 2rem
	}

	.profile__links a {
		display: inline-block;
		width: 4rem;
		height: 4rem;
		font-size: 0
	}

	.profile__links a:before {
		content: "";
		display: block;
		width: 4rem;
		height: 4rem;
		background-position: 50%;
		background-size: 100%;
		background-repeat: no-repeat
	}

	.profile__links_linkedin {
		margin-right: 2rem
	}

	.profile__links_linkedin a:before {
		background-image: url(/themes/cam/images/linkedin.svg)
	}

	.profile__links_website {
		height: 4rem
	}

	.profile__links_website a:before {
		background-image: url(/themes/cam/images/web.svg);
		background-position: 50%;
		background-size: 100%;
		background-repeat: no-repeat
	}

	.profile__link {
		display: block;
		margin-top: 1rem
	}

	.profile__link .read_more_icon {
		display: inline-block;
		max-width: .5rem;
		padding-bottom: 0
	}

	.node--type-profile+.node--type-profile {
		margin-top: 4rem
	}

	@media screen and (min-width:64em) {
		.node--type-profile+.node--type-profile {
			margin-bottom: 6rem
		}
	}

	.paragraph--type--student-athlete-profile {
		margin-bottom: 4rem
	}

	.paragraph--faculty-staff-profile--page--default .profile__content_page,
	.paragraph--faculty-staff-profile--page--listing-mode .profile__content_page,
	.paragraph--type--coach-profile .profile__content_page {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
		margin-top: 4rem
	}

	@media screen and (min-width:35em) {

		.paragraph--faculty-staff-profile--page--default .profile__content_page,
		.paragraph--faculty-staff-profile--page--listing-mode .profile__content_page,
		.paragraph--type--coach-profile .profile__content_page {
			-ms-flex-wrap: nowrap;
			flex-wrap: nowrap
		}
	}

	@media screen and (min-width:35em) {

		.paragraph--faculty-staff-profile--page--default .profile__pic,
		.paragraph--faculty-staff-profile--page--listing-mode .profile__pic,
		.paragraph--type--coach-profile .profile__pic {
			margin-bottom: 0
		}
	}

	.paragraph--faculty-staff-profile--page--default .profile__info p,
	.paragraph--faculty-staff-profile--page--listing-mode .profile__info p,
	.paragraph--type--coach-profile .profile__info p {
		margin-bottom: 1rem
	}

	.paragraph--faculty-staff-profile--page--default .profile__background,
	.paragraph--faculty-staff-profile--page--listing-mode .profile__background,
	.paragraph--type--coach-profile .profile__background {
		margin: 0;
		padding: 0
	}

	.paragraph--faculty-staff-profile--page--default .profile__background,
	.paragraph--faculty-staff-profile--page--default .profile__background p,
	.paragraph--faculty-staff-profile--page--listing-mode .profile__background,
	.paragraph--faculty-staff-profile--page--listing-mode .profile__background p,
	.paragraph--type--coach-profile .profile__background,
	.paragraph--type--coach-profile .profile__background p {
		color: #71716f;
		font-size: 1.4rem;
		line-height: 1.57;
		letter-spacing: -.018rem
	}

	.faculty_list__inner .links.inline,
	.paragraph--type--student-athlete-profile-list .links.inline {
		display: none
	}

	@media screen and (min-width:64em) {

		.faculty_list__inner article:last-child,
		.paragraph--type--student-athlete-profile-list article:last-child {
			margin-bottom: 0
		}
	}

	@media screen and (min-width:75em) {

		.faculty_list__inner,
		.paragraph--type--student-athlete-profile-list {
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex;
			-ms-flex-wrap: wrap;
			flex-wrap: wrap;
			max-width: calc(100% + 4rem);
			margin: -2rem
		}

		.faculty_list__inner .profile__content_page,
		.paragraph--type--student-athlete-profile-list .profile__content_page {
			margin-top: 0
		}

		.faculty_list__inner .node--type-profile,
		.paragraph--type--student-athlete-profile-list .node--type-profile {
			width: calc(50% - 4rem);
			margin: 3rem 2rem
		}

		.faculty_list__inner .node--type-profile+.node--type-profile,
		.paragraph--type--student-athlete-profile-list .node--type-profile+.node--type-profile {
			margin: 3rem 2rem
		}

		.faculty_list__inner .profile__pic,
		.paragraph--type--student-athlete-profile-list .profile__pic {
			margin-right: 2rem
		}
	}

	.faculty_list__inner h2 {
		width: 100%
	}

	@media screen and (min-width:75em) {
		.faculty_list__inner h2 {
			margin-left: 2rem
		}
	}

	.page-node-type-profile .content-header .page_title {
		margin-bottom: 0
	}

	@media screen and (min-width:64em) {
		.page-node-type-profile .content-header {
			padding-right: 0
		}

		.page-node-type-profile .content-header .page_title {
			max-width: 64rem;
			margin-right: auto;
			margin-left: auto
		}
	}

	.page-node-type-profile .content-area {
		padding-top: 1rem;
		padding-bottom: 6rem
	}

	@media screen and (min-width:64em) {
		.page-node-type-profile .content-area {
			max-width: 64rem;
			margin-right: auto;
			margin-left: auto;
			padding-bottom: 8rem
		}
	}

	@media screen and (min-width:75em) {
		.page-node-type-profile .content-area {
			padding-bottom: 16rem
		}
	}

	@media screen and (min-width:75em) {
		.page-node-type-profile .main_content {
			position: relative;
			z-index: 1
		}

		.page-node-type-profile .main_content:after,
		.page-node-type-profile .main_content:before {
			content: "";
			position: absolute;
			z-index: -1
		}

		.page-node-type-profile .main_content:before {
			top: 10.8rem;
			left: -500%;
			width: 1100%;
			height: 100%;
			background-color: #f5fcef
		}

		.page-node-type-profile .main_content:after {
			top: 0;
			left: -4rem;
			width: 1000%;
			height: calc(100% - 8rem);
			background-color: #fff
		}

		.page-node-type-profile .has-hero .main_content:before {
			top: 15rem;
			height: calc(100% - 15rem)
		}
	}

	.program-content+div {
		padding-top: 4rem
	}

	@media screen and (min-width:64em) {
		.program-content+div {
			padding-top: 8rem
		}
	}

	@media screen and (min-width:67.5em) {
		.program__content .program__overview h2 {
			font-size: 4rem
		}
	}

	.program__content .program__overview .text_image {
		margin-right: auto;
		margin-bottom: 0;
		margin-left: auto;
		padding-top: 0;
		padding-bottom: 0
	}

	@media screen and (min-width:46.875em) {
		.program__content .program__overview .text_image {
			margin-top: 4rem
		}
	}

	@media screen and (min-width:64em) {
		.program__content .program__overview .text_image {
			margin-top: 8rem
		}
	}

	.program__content .program__overview .program__info-wrapper {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-flow: wrap;
		flex-flow: wrap;
		margin-right: -2rem;
		margin-left: -2rem
	}

	.program__content .program__overview .program__info-wrapper .program__info {
		width: 100%;
		margin-right: 2rem;
		margin-bottom: 2rem;
		margin-left: 2rem
	}

	.program__content .program__overview .program__info-wrapper .program__info b {
		color: #4d4d4c;
		font-size: 2rem
	}

	.program__content .program__overview .program__info-wrapper .program__info a,
	.program__content .program__overview .program__info-wrapper .program__info p {
		font-size: 1.8rem
	}

	.program__content .program__overview .program__info-wrapper .program__info p {
		margin-bottom: .5rem
	}

	.program__content .program__overview .program__info-wrapper .program__info p+p {
		margin-bottom: 0
	}

	.program__content .program__overview .program__info-wrapper .program__info a {
		display: block
	}

	.program__content .program__overview .program__info-wrapper .program__info a+a {
		margin-top: .8rem
	}

	.program__content .program__overview .program__info-wrapper .program__info a[href=""] {
		display: none
	}

	@media screen and (min-width:46.875em) {
		.program__content .program__overview .program__info-wrapper .program__info {
			width: calc(50% - 4rem)
		}
	}

	.program__content .program__overview a.cta_button {
		width: 100%;
		max-width: 35rem
	}

	.program__content .program__overview .page_block__image {
		position: absolute;
		display: none
	}

	@media screen and (min-width:46.875em) {
		.program__content .program__overview .page_block__image {
			display: inline-block;
			margin-left: calc(35% + 4rem)
		}
	}

	@media screen and (min-width:64em) {
		.program__content .program__overview .page_block__image {
			margin-left: calc(35% - 4rem)
		}
	}

	.program__content .program__overview .page_block__text {
		margin-left: 4rem
	}

	.program__content .program-tabs {
		margin-right: 1rem;
		margin-left: 1rem;
		white-space: nowrap;
		-ms-overflow-style: -ms-autohiding-scrollbar
	}

	.program__content .program-tabs.mobile-tabs {
		overflow-x: auto;
		padding-top: 3.5rem
	}

	.program__content .program-tabs.mobile-tabs:before {
		content: "\02190\02002Scroll for more\02002\02192";
		position: absolute;
		top: .5rem;
		left: -1rem;
		z-index: 2;
		display: block;
		width: calc(100% + 1rem);
		color: #9a9a9a;
		font-size: 1rem;
		text-align: center;
		text-transform: uppercase
	}

	.program__content .program-tabs .tablinks {
		position: relative;
		display: inline-block;
		padding: 2rem 3rem;
		background-color: transparent;
		color: #5a7826;
		-webkit-box-shadow: none;
		box-shadow: none;
		outline: none;
		font-weight: 400;
		font-size: 1.6rem;
		text-transform: none;
		cursor: pointer
	}

	.program__content .program-tabs .tablinks.active {
		background-color: #fff;
		color: #71716f
	}

	@media screen and (min-width:40em) {
		.program__content .program-tabs .tablinks {
			padding: 4rem 3rem 3.5rem
		}
	}

	@media screen and (min-width:46.875em) {
		.program__content .program-tabs .tablinks {
			font-size: 1.8rem
		}
	}

	@media screen and (min-width:64em) {
		.program__content .program-tabs .tablinks {
			font-size: 2rem
		}
	}

	@media screen and (min-width:64em) {
		.program__content .program-tabs {
			margin-right: 0;
			margin-left: 0
		}
	}

	.program__content .program__container {
		margin-right: -4rem;
		margin-left: -4rem;
		padding-top: 4rem;
		padding-bottom: 4rem;
		background-color: #f5fcef
	}

	.program__content .program__container .tabcontent {
		display: none;
		overflow: auto;
		margin-right: 1rem;
		margin-left: 1rem;
		padding: 4rem 3rem;
		background-color: #fff
	}

	@media screen and (max-width:47.9375em) {
		.program__content .program__container .tabcontent h2 {
			margin-left: 2rem
		}
	}

	.program__content .program__container .tabcontent.program__admission .requirements-table table td {
		padding: 1rem
	}

	.program__content .program__container .tabcontent.program__admission .requirements-table table th {
		padding: 1rem;
		text-align: left
	}

	@media screen and (min-width:64em) {
		.program__content .program__container .tabcontent {
			margin-right: 0;
			margin-left: 0;
			padding: 6rem 0 5.5rem
		}
	}

	.program__content .program__container .tabcontent_inner {
		max-width: 64rem;
		margin-right: auto;
		margin-left: auto
	}

	@media screen and (min-width:64em) {
		.program__content .program__container {
			position: relative;
			margin-right: 0;
			margin-left: 0;
			padding-top: 8rem;
			padding-bottom: 8rem
		}

		.program__content .program__container:before {
			content: "";
			position: absolute;
			top: 0;
			left: -100%;
			width: 1000%;
			height: 100%;
			background-color: #f5fcef
		}

		.program__content .program__container .container {
			padding-right: 0;
			padding-left: 0
		}
	}

	@media screen and (min-width:64em) {
		.program__content .program__faculty .tabcontent_inner {
			max-width: 100%;
			padding-right: 8rem;
			padding-left: 8rem
		}
	}

	@media screen and (min-width:64em) {
		.program__content .tab_faculty_listing {
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex;
			-ms-flex-wrap: wrap;
			flex-wrap: wrap;
			margin: 2rem -2rem -1rem
		}

		.program__content .tab_faculty_listing .profile__content_page {
			margin-top: 0
		}

		.program__content .tab_faculty_listing .node--type-profile {
			width: calc(50% - 4rem);
			margin: 3rem 2rem
		}

		.program__content .tab_faculty_listing .node--type-profile+.node--type-profile {
			margin: 3rem 2rem
		}

		.program__content .tab_faculty_listing .profile__pic {
			margin-right: 2rem
		}
	}

	.program__content .faculty_link {
		position: relative;
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center
	}

	.program__content .faculty_link:before {
		content: "";
		margin-left: auto
	}

	.program__content .faculty_link img {
		margin-top: -.2rem;
		padding-left: .4rem
	}

	.page-node-type-program .container-program {
		position: relative;
		left: calc(24% - 1rem);
		max-width: 114rem
	}

	.page-node-type-program .intro-text,
	.page-node-type-program .intro-text-about {
		margin-top: 4rem
	}

	.page-node-type-program .intro-text-about p,
	.page-node-type-program .intro-text p {
		color: #71716f;
		font-size: 1.8rem;
		letter-spacing: -.022rem
	}

	@media screen and (min-width:46.875em) {

		.page-node-type-program .intro-text-about p,
		.page-node-type-program .intro-text p {
			font-size: 2rem
		}
	}

	@media screen and (min-width:64em) {

		.page-node-type-program .intro-text-about p,
		.page-node-type-program .intro-text p {
			font-size: 2.2rem
		}
	}

	@media screen and (min-width:90em) {

		.page-node-type-program .intro-text-about p,
		.page-node-type-program .intro-text p {
			font-size: 2.4rem
		}
	}

	.page-node-type-program .image-about {
		margin: 4rem 0
	}

	.page-node-type-program .program__overview .page_block__text {
		width: 100%;
		margin-right: 0;
		margin-left: 0;
		padding-top: 4rem;
		padding-bottom: 4rem
	}

	@media screen and (min-width:46.875em) {
		.page-node-type-program .program__overview .page_block__text {
			left: 0;
			max-width: 40rem
		}
	}

	@media screen and (min-width:64em) {
		.page-node-type-program .program__overview .page_block__text {
			max-width: 45rem;
			padding-top: 8rem;
			padding-bottom: 5rem
		}
	}

	@media screen and (min-width:67.5em) {
		.page-node-type-program .program__overview .page_block__text {
			max-width: 48rem
		}
	}

	@media screen and (min-width:75em) {
		.page-node-type-program .program__overview .page_block__text {
			max-width: 50rem
		}
	}

	.page-node-type-program .program__overview .icon-bg-angle {
		left: -.1rem
	}

	@media screen and (min-width:64em) {
		.page-node-type-program .program__overview .call_to_action_button {
			padding-right: 0;
			padding-left: 0
		}

		.page-node-type-program .program__overview .cta_button__wrapper {
			margin-top: 4rem
		}
	}

	.page-node-type-program .paragraph--type--call-to-action .page_block__text {
		margin-right: 0
	}

	@media screen and (min-width:64em) {
		.page-node-type-program .call_to_action .page_block__text {
			width: calc(50% - 4rem)
		}
	}

	.page-programs-courses-find-program .content-area {
		position: relative
	}

	.page-programs-courses-find-program .content-area:before {
		content: "";
		position: absolute;
		top: 0;
		left: -500%;
		width: 1100%;
		height: 100%;
		background-color: #f5fcef
	}

	.page-programs-courses-find-program .call_to_action {
		position: relative;
		z-index: 1;
		margin-top: 8rem;
		margin-bottom: -4rem
	}

	.page-programs-courses-find-program .call_to_action:before {
		content: "";
		position: absolute;
		top: 0;
		left: -500%;
		z-index: -1;
		width: 1100%;
		height: 100%;
		background-color: #fff
	}

	@media screen and (min-width:64em) {
		.page-programs-courses-find-program .call_to_action {
			margin-top: 10rem;
			margin-bottom: -5rem
		}
	}

	@media screen and (min-width:75em) {
		.page-programs-courses-find-program .call_to_action {
			margin-bottom: -8rem
		}
	}

	@media screen and (min-width:64em) {
		.page-programs-courses-find-program .page_title {
			margin-bottom: 4rem
		}

		.page-programs-courses-find-program .content-area {
			margin-right: -4rem;
			margin-left: -4rem
		}

		.page-programs-courses-find-program .content-area .container {
			padding-right: 0;
			padding-left: 0
		}

		.page-programs-courses-find-program .body-content {
			padding-right: 4rem;
			padding-left: 4rem
		}
	}

	@media screen and (min-width:75em) {
		.page-programs-courses-find-program .page_title {
			margin-bottom: 8rem
		}
	}

	.landing__popular_content {
		position: relative;
		padding-top: 4rem;
		padding-bottom: 4rem
	}

	@media screen and (min-width:64em) {
		.landing__popular_content {
			margin-bottom: 8rem;
			padding-top: 4.5rem
		}
	}

	.landing__popular_content:before {
		position: absolute;
		z-index: -2;
		top: 0;
		left: -50%;
		width: 1000%;
		height: 100%;
		content: "";
		background-color: #f5fcef
	}

	@media screen and (min-width:64em) {
		.landing__popular_content:before {
			left: -48rem
		}
	}

	.landing__popular_content .landing__link_list {
		margin-bottom: 0
	}

	.landing__popular_content .icon-bg-angle {
		z-index: -1;
		left: -50%;
		width: 1000%;
		-webkit-transform: scaleX(-1);
		transform: scaleX(-1)
	}

	@media screen and (min-width:64em) {
		.landing__popular_content .icon-bg-angle {
			left: -48rem
		}
	}

	.contact_info {
		position: relative;
		padding-top: 4rem;
		padding-bottom: 4rem
	}

	.contact_info:before {
		content: "";
		position: absolute;
		top: 0;
		left: 56%;
		z-index: -2;
		width: 1000%;
		height: 100%;
		background-color: #f5fcef
	}

	@media screen and (min-width:64em) {
		.contact_info:before {
			left: 64%
		}
	}

	@media screen and (min-width:75em) {
		.contact_info:before {
			left: 65%
		}
	}

	.contact_info .icon-bg-angle {
		position: absolute;
		right: 0;
		left: 56%;
		z-index: -2;
		width: calc(100% + 8rem);
		-webkit-transform: scaleX(-1);
		transform: scaleX(-1)
	}

	@media screen and (min-width:64em) {
		.contact_info .icon-bg-angle {
			left: 64%
		}
	}

	@media screen and (min-width:75em) {
		.contact_info .icon-bg-angle {
			left: 65%
		}
	}

	.contact_info span {
		display: block
	}

	.contact_info p {
		margin-bottom: 0
	}

	.contact_info h2 {
		margin-bottom: 2rem
	}

	@media screen and (min-width:64em) {
		.contact_info h2 {
			margin-bottom: 6rem
		}
	}

	@media screen and (min-width:67.5em) {
		.contact_info h2 {
			font-size: 4rem
		}
	}

	.contact_info .contact-name {
		color: #4d4d4c;
		font-weight: 700;
		font-size: 1.8rem;
		font-family: lexia, Helvetica Neue, helvetica, Arial, sans-serif;
		line-height: 1.5;
		letter-spacing: -.012rem
	}

	@media screen and (min-width:64em) {
		.contact_info .contact-name {
			font-size: 2rem
		}
	}

	@media screen and (min-width:67.5em) {
		.contact_info .contact-name {
			font-size: 2.4rem
		}
	}

	.contact_info .paragraph--type--contact-campus-location {
		margin-top: 1.5rem
	}

	.contact_info .contact-details {
		display: block;
		padding-top: 1.5rem;
		color: #71716f;
		font-size: 1.6rem;
		font-family: univers, Helvetica Neue, helvetica, Arial, sans-serif;
		line-height: 1.5;
		letter-spacing: -.02rem
	}

	.contact_info .contact-details a {
		color: #5a7826;
		line-height: 1.5;
		letter-spacing: -.02rem
	}

	@media screen and (min-width:64em) {
		.contact_info {
			padding-top: 8rem;
			padding-bottom: 8rem
		}
	}

	.page-node-type-page .main_content {
		padding-bottom: 4rem
	}

	@media screen and (min-width:64em) {
		.page-node-type-page .main_content {
			padding-bottom: 8rem
		}
	}

	.page-node-type-page .page_blocks {
		margin-bottom: -4rem
	}

	@media screen and (min-width:64em) {
		.page-node-type-page .page_blocks {
			margin-bottom: -8rem
		}
	}

	.page-node-type-page .paragraph--type--related-information {
		margin-left: 0
	}

	.page-node-type-page .intro_text p {
		font-size: 2.2rem;
		font-weight: 300;
		line-height: 1.36;
		letter-spacing: -.02rem
	}

	.page-node-type-page .paragraph--type--call-to-action .page_block__text {
		min-height: 0
	}

	.page-node-type-page li.node-readmore {
		display: none
	}

	@media screen and (min-width:64em) {

		.landing__intro_text,
		.landing__popular_content .landing__link_list,
		.landing__related .landing__link_list,
		.landing__section__lists {
			margin-left: 4rem
		}
	}

	@media screen and (min-width:75em) {

		.landing__intro_text,
		.landing__popular_content .landing__link_list,
		.landing__related .landing__link_list,
		.landing__section__lists {
			margin-left: 8rem
		}
	}

	.landing__link_list {
		margin-bottom: 4rem
	}

	.landing__link {
		margin-bottom: 1rem
	}

	.landing__link a {
		font-size: 1.8rem;
		color: #5a7826
	}

	.landing__link a:active,
	.landing__link a:hover {
		color: #93bf37
	}

	.landing__intro_text,
	.landing__popular_content {
		margin-bottom: 4rem
	}

	@media screen and (min-width:64em) {

		.landing__intro_text,
		.landing__popular_content {
			margin-bottom: 8rem
		}
	}

	.landing__intro_text {
		padding-right: 0;
		padding-left: 0
	}

	@media screen and (min-width:64em) {
		.landing__section {
			margin-bottom: 4rem
		}
	}

	@media screen and (min-width:90em) {
		.landing__section {
			margin-bottom: 6rem
		}
	}

	.landing__intro_text p {
		font-weight: 300
	}

	@media screen and (min-width:46.875em) {
		.landing__section__lists {
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex;
			-ms-flex-wrap: wrap;
			flex-wrap: wrap
		}
	}

	@media screen and (min-width:46.875em) {
		.landing__section_links {
			-ms-flex-preferred-size: 50%;
			flex-basis: 50%;
			padding-right: 4rem
		}
	}

	@media screen and (min-width:64em) {
		.landing__section_links {
			-ms-flex-preferred-size: 33.33333%;
			flex-basis: 33.33333%
		}
	}

	.article_meta {
		margin-bottom: 5rem
	}

	.article_meta p {
		font-size: 1.4rem
	}

	.article_meta__inner {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center
	}

	.article_meta__inner .addtoany_share {
		margin-top: -.2rem;
		padding-left: 1rem
	}

	.article_meta__inner p {
		margin-bottom: 0
	}

	.article_meta__inner p.comma:last-child {
		display: none
	}

	.article_meta__inner .comma-seperated span:after {
		display: inline;
		margin-right: -.1rem;
		margin-left: -.5rem;
		content: ","
	}

	.article_meta__inner .comma-seperated span:last-child:after {
		display: none;
		content: ""
	}

	button.social_share {
		width: 4.2rem;
		height: 4.2rem;
		margin-top: -1rem;
		margin-bottom: -1rem;
		padding: 1rem;
		-webkit-box-shadow: none;
		box-shadow: none
	}

	button.social_share,
	button.social_share:focus,
	button.social_share:hover {
		background-color: transparent
	}

	.share_icon {
		width: 2.2rem;
		height: 2.2rem;
		fill: #757575
	}

	.game_results p {
		font-weight: 700;
		text-transform: uppercase
	}

	.game_results+.intro_text {
		margin-top: 2rem
	}

	.inline_image {
		margin-bottom: 5rem
	}

	.page-node-type-article .call_to_action,
	.page-node-type-article .contact_info {
		margin-top: 4rem
	}

	@media screen and (min-width:64em) {

		.page-node-type-article .call_to_action,
		.page-node-type-article .contact_info {
			margin-top: 8rem
		}
	}

	.page-node-type-article .call_to_action+.contact_info {
		margin-top: 0
	}

	.page-node-type-article .call_to_action .page_block__image {
		width: calc(100% + 8rem);
		margin-bottom: 0
	}

	@media screen and (min-width:46.875em) {
		.page-node-type-article .call_to_action .page_block__image {
			width: 70%
		}
	}

	@media screen and (min-width:53.75em) {
		.page-node-type-article .call_to_action .page_block__image {
			width: calc(70% - 1rem)
		}
	}

	@media screen and (min-width:64em) {
		.page-node-type-article .call_to_action .page_block__image {
			width: calc(70% + 6rem)
		}
	}

	@media screen and (min-width:67.5em) {
		.page-node-type-article .call_to_action .page_block__image {
			width: calc(80% + 1rem)
		}
	}

	@media screen and (min-width:75em) {
		.page-node-type-article .call_to_action .page_block__image {
			width: calc(90% + 3rem)
		}
	}

	.page-node-type-article .call_to_action .page_block__image img {
		padding-bottom: 0
	}

	.page-node-type-article .intro_text {
		padding-right: 0;
		padding-left: 0
	}

	.page-node-type-article .intro_text p {
		font-family: univers, Helvetica Neue, helvetica, Arial, sans-serif;
		font-size: 2.2rem;
		font-weight: 300;
		line-height: 1.36;
		letter-spacing: -.02rem;
		color: #71716f
	}

	.page-node-type-article .breadcrumb ol {
		margin-left: -1rem
	}

	@media screen and (min-width:64em) {
		.page-node-type-article .has-sidebar .call_to_action .page_block__image {
			width: calc(70% + 5rem)
		}
	}

	@media screen and (min-width:67.5em) {
		.page-node-type-article .has-sidebar .call_to_action .page_block__image {
			width: calc(90% - 1rem)
		}
	}

	@media screen and (min-width:75em) {
		.page-node-type-article .has-sidebar .call_to_action .page_block__image {
			width: calc(100% + 5rem)
		}
	}

	.news-listing .article-listings {
		-webkit-box-orient: vertical;
		-webkit-box-direction: normal;
		-ms-flex-direction: column;
		flex-direction: column
	}

	@media screen and (min-width:30em) {
		.news-listing .article-listings {
			-webkit-box-orient: horizontal;
			-webkit-box-direction: normal;
			-ms-flex-direction: row;
			flex-direction: row
		}
	}

	.news-listing .article-listing__date {
		margin-bottom: 2rem
	}

	@media screen and (min-width:30em) {
		.news-listing .article-listing__date {
			margin-bottom: 0
		}
	}

	.news-listing .intro_text {
		margin-top: 1rem;
		margin-bottom: 0;
		padding-right: 0;
		padding-left: 0
	}

	.news-listing .intro_text p {
		font-size: 1.6rem;
		line-height: 1.5;
		letter-spacing: -.02rem;
		color: #71716f
	}

	.news-listing .article-listing__title {
		font-size: 1.4rem
	}

	.news-listing .article-listing__title h2 {
		margin-bottom: .5rem
	}

	.news-listing .article-listing__date {
		padding-right: 2.6rem
	}

	.news-listing .date-spacer {
		border-right-width: .6rem
	}

	@media screen and (min-width:46.875em) {
		.page-node-type-student-funding .call_to_action .page_block__text {
			width: calc(60% - 5rem)
		}
	}

	.awards-section {
		position: relative;
		padding-bottom: 5rem
	}

	.awards-section:after,
	.awards-section:before {
		content: "";
		position: absolute;
		background-color: #f5fcef
	}

	.awards-section:after {
		bottom: 0;
		left: -500%;
		width: 1100%;
		height: 4rem
	}

	@media screen and (min-width:64em) {
		.awards-section:after {
			height: 8rem
		}
	}

	@media screen and (min-width:64em) {
		.awards-section {
			padding-top: 8rem;
			padding-bottom: 9rem
		}
	}

	@media screen and (min-width:75em) {
		.awards-section:before {
			top: 0;
			right: calc(100% + 8rem);
			width: 1000%;
			height: 100%
		}
	}

	.awards-at-glance {
		width: 100%;
		max-width: 64rem;
		margin-top: 2rem;
		margin-right: -2rem;
		margin-bottom: 4rem
	}

	.awards-at-glance .inner {
		margin-bottom: 2rem
	}

	.awards-at-glance .inner span {
		color: #71716f
	}

	.awards-at-glance .inner span,
	.awards-at-glance .inner span p {
		font-size: 1.8rem
	}

	@media screen and (min-width:64em) {

		.awards-at-glance .inner span,
		.awards-at-glance .inner span p {
			font-size: 2rem
		}
	}

	.awards-at-glance .inner span p {
		margin-bottom: .5rem;
		color: #4d4d4c;
		font-weight: 700
	}

	@media screen and (min-width:64em) {
		.awards-at-glance .inner {
			width: calc(50% - 2rem);
			margin-right: 2rem
		}
	}

	@media screen and (min-width:64em) {
		.awards-at-glance {
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex;
			-ms-flex-wrap: wrap;
			flex-wrap: wrap;
			margin-top: 6rem;
			margin-bottom: 5rem
		}
	}

	.awards-inner-2 {
		position: relative;
		z-index: 1;
		padding-bottom: 4rem
	}

	@media screen and (min-width:64em) {
		.awards-inner-2 {
			padding-bottom: 8rem
		}
	}

	@media screen and (min-width:75em) {
		.awards-inner-2 {
			padding-bottom: 16rem
		}

		.awards-inner-2:after,
		.awards-inner-2:before {
			content: "";
			position: absolute;
			z-index: -1;
			background-color: #f5fcef
		}

		.awards-inner-2:before {
			top: 0;
			left: 100%;
			width: 1000%;
			height: 100%
		}

		.awards-inner-2:after {
			bottom: 0;
			left: -500%;
			width: 1100%;
			height: 8rem
		}
	}

	.awards-criteria {
		max-width: 64rem
	}

	.selection-process {
		max-width: 64rem;
		padding-top: 2rem
	}

	.selection-process h2 {
		margin-top: 4rem
	}

	.selection-process+div:not(.cta_button__wrapper) {
		margin-top: 6rem
	}

	.awards-description {
		max-width: 64rem;
		padding-top: 4rem;
		padding-bottom: 4rem
	}

	@media screen and (min-width:64em) {
		.awards-description {
			padding-top: 8rem
		}
	}

	@media screen and (min-width:75em) {
		.page-node-type-student-funding .content-header {
			position: relative
		}

		.page-node-type-student-funding .content-header:before {
			content: "";
			position: absolute;
			top: 6.8rem;
			right: calc(100% + 8rem);
			display: block;
			width: 1000%;
			height: calc(100% + 16rem);
			background-color: #f5fcef
		}
	}

	.page-node-type-student-funding .has-hero .content-header:before {
		top: 11rem;
		height: 100%
	}

	.node--type-student-funding .cta_button__wrapper {
		margin-top: 5rem;
		margin-bottom: 2rem
	}

	.node--type-student-funding .donor {
		max-width: 64rem;
		padding-top: 0
	}

	.cam-funding-listing {
		margin-bottom: 4rem
	}

	@media screen and (min-width:64em) {
		.cam-funding-listing {
			margin-bottom: 8rem
		}
	}

	.cam-funding-listing .deadline-filter {
		padding-top: 2rem;
		padding-bottom: 2rem;
		border-top: .1rem solid #cfd8c7
	}

	.cam-funding-listing .deadline-filter .pos-r label {
		font-family: lexia, Helvetica Neue, helvetica, Arial, sans-serif;
		font-size: 2rem;
		font-weight: 600;
		line-height: 1.8;
		letter-spacing: -.017rem;
		color: #4d4d4c
	}

	.cam-funding-listing .deadline-filter input {
		width: 100%;
		-webkit-box-shadow: none;
		box-shadow: none
	}

	.cam-funding-listing .student-filter .form-item {
		padding-bottom: 0
	}

	.cam-funding-listing .dollar-value {
		margin-top: 1rem
	}

	.cam-funding-listing .views-row {
		padding: 4rem
	}

	.cam-funding-listing .views-row:last-child {
		margin-bottom: 4rem
	}

	.cam-funding-listing .views-row h2 {
		font-size: 2rem;
		margin-bottom: 2rem
	}

	.cam-funding-listing .views-row h2:before {
		position: absolute;
		top: .4rem;
		left: -1.8rem;
		content: "";
		border-bottom: 1.6rem solid transparent;
		border-left: 1.1rem solid #93bf37
	}

	.cam-funding-listing .views-no-results {
		margin-right: 2rem;
		margin-left: 2rem
	}

	@media screen and (min-width:64em) {
		.cam-funding-listing .views-no-results {
			margin-right: 4rem;
			margin-left: 4rem
		}
	}

	.page-node-type-event .content-area {
		padding-bottom: 6rem
	}

	@media screen and (min-width:64em) {
		.page-node-type-event .content-area {
			padding-bottom: 8rem
		}
	}

	.page-node-type-event p.label {
		font-weight: 700;
		line-height: 1.38;
		margin-bottom: .5rem;
		letter-spacing: -.02rem;
		color: #535353
	}

	.page-node-type-event .main_content .container>div {
		margin-top: 2rem;
		margin-bottom: 2rem
	}

	.page-node-type-event .main_content .container>div:last-child {
		margin-bottom: 0
	}

	.page-node-type-event .social-share {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex
	}

	.page-node-type-event .social-share p {
		padding-top: .7rem;
		padding-right: 1rem
	}

	.page-node-type-event h1.page_title {
		font-size: 3.4rem;
		line-height: 1.24;
		letter-spacing: -.02rem
	}

	.page-node-type-event .image_only {
		padding-bottom: 0
	}

	.event-date,
	.event-location,
	.node--type-event .category,
	.node--type-event .last-updated {
		font-weight: 300
	}

	.event-scores {
		position: relative;
		margin-top: 1rem;
		margin-bottom: 1rem;
		padding-right: 2rem;
		padding-left: 2.6rem
	}

	.event-scores:before {
		position: absolute;
		top: -1rem;
		left: 0;
		height: calc(100% + 2rem);
		content: "";
		border-right: .6rem solid #5a7826;
		border-bottom: .2rem solid transparent
	}

	.event-scores .college-teams {
		font-weight: 700;
		letter-spacing: .06rem;
		text-transform: uppercase
	}

	.event-date .date-recur-date,
	.event-date .date-recur-occurrences li,
	.scores-recap {
		line-height: 1.38;
		letter-spacing: -.02rem
	}

	.chargers-event,
	.college-event {
		padding-top: 2rem;
		padding-bottom: 2rem
	}

	@media screen and (min-width:64em) {

		.chargers-event,
		.college-event {
			padding-top: 6rem;
			padding-bottom: 6rem
		}
	}

	.chargers-event h2,
	.college-event h2 {
		margin-top: 2rem;
		margin-bottom: 2rem
	}

	.block-views-blockevents-listing-block-1 {
		position: relative
	}

	.block-views-blockevents-listing-block-1 .views-field-field-event-date-end-value {
		font-size: 1.4rem
	}

	.block-views-blockevents-listing-block-1 .views-field-field-event-date-end-value .field-content {
		position: relative
	}

	.block-views-blockevents-listing-block-1 .views-field-field-event-date-end-value .field-content:before {
		position: absolute;
		top: -.1rem;
		left: -1.8rem;
		content: "";
		border-bottom: 1.6rem solid transparent;
		border-left: 1.1rem solid #93bf37
	}

	.block-views-blockevents-listing-block-1 .views-field-field-event-date-end-value~div {
		margin-left: 3.3rem
	}

	.block-views-blockevents-listing-block-1 .views-field-title {
		margin-top: .5rem
	}

	.block-views-blockevents-listing-block-1 .views-field-title a {
		font-size: 1.8rem;
		font-weight: 700;
		line-height: 1.5;
		text-decoration: none;
		letter-spacing: -.015rem;
		color: #5a7826
	}

	.block-views-blockevents-listing-block-1 .views-field-title a:active,
	.block-views-blockevents-listing-block-1 .views-field-title a:hover {
		text-decoration: underline
	}

	.block-views-blockevents-listing-block-1 .views-field-field-event-date-2,
	.block-views-blockevents-listing-block-1 .views-field-field-event-date-2+div {
		margin-top: .5rem
	}

	.block-views-blockevents-listing-block-1 .views-field-field-event-date-2~div {
		font-size: 1.4rem
	}

	.block-quicktabs-blockprogram-course-listing .row-content {
		margin-top: 8rem
	}

	@media screen and (min-width:64em) {
		.block-quicktabs-blockprogram-course-listing .row-content {
			margin-top: 0
		}
	}

	.block-quicktabs-blockprogram-course-listing .row-content .page-title,
	.block-quicktabs-blockprogram-course-listing .row-content .views-no-results {
		margin-left: 2rem
	}

	.block-quicktabs-blockprogram-course-listing .row-content .page-title {
		position: relative;
		margin-bottom: 4rem
	}

	.block-quicktabs-blockprogram-course-listing .row-content .page-title:before {
		position: absolute;
		top: .6rem;
		left: -1.8rem;
		content: "";
		border-bottom: 1.6rem solid transparent;
		border-left: 1.1rem solid #93bf37
	}

	.block-quicktabs-blockprogram-course-listing .views-row .views-field-title {
		background: #fff
	}

	.block-quicktabs-blockprogram-course-listing .views-row .views-field-title span a {
		font-size: 1.4rem;
		font-weight: 400;
		font-style: normal;
		font-stretch: normal;
		line-height: normal;
		display: block;
		padding: 2.4rem 2rem 2rem;
		letter-spacing: -.018rem;
		color: #5a7826
	}

	.block-quicktabs-blockprogram-course-listing .views-row:nth-of-type(odd) .views-field-title {
		background: #f5fcef
	}

	.block-quicktabs-blockprogram-course-listing .views-exposed-form {
		padding: 0;
		-webkit-box-shadow: none;
		box-shadow: none
	}

	.block-quicktabs-blockprogram-course-listing .views-exposed-form details:last-of-type {
		padding-bottom: 0
	}

	.pager {
		position: relative;
		margin-top: 4rem;
		margin-right: 4rem;
		margin-left: 4rem;
		padding-top: 4rem;
		border-top: .1rem solid #cfd8c7
	}

	.pager .pager__items,
	.pager .pager__pages {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center
	}

	.pager .pager__items {
		margin-bottom: -2rem
	}

	.pager .pager__pages {
		-webkit-box-flex: 1;
		-ms-flex-positive: 1;
		flex-grow: 1;
		-ms-flex-negative: 1;
		flex-shrink: 1;
		width: calc(100% - 9.6rem)
	}

	.pager .pager__item--previous+.pager__pages {
		margin-left: 0
	}

	.pager .pager__item {
		position: relative;
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
		min-width: 2.9rem;
		height: 2.9rem;
		margin-right: 1.5rem;
		margin-bottom: 2rem;
		padding: .5rem .4rem .3rem;
		border: .1rem solid #5a7826;
		line-height: 1;
		-webkit-transition: border-color .3s ease;
		transition: border-color .3s ease
	}

	.pager .pager__item:hover {
		border-color: #93bf37
	}

	.pager .pager__item a {
		text-decoration: none !important;
		-webkit-transition: color .3s ease;
		transition: color .3s ease
	}

	.pager .pager__item a:before {
		content: "";
		position: absolute;
		top: -.75rem;
		left: -.75rem;
		width: calc(100% + 1.5rem);
		height: calc(100% + 1.5rem)
	}

	.pager .pager__item a:focus,
	.pager .pager__item a:hover {
		text-decoration: none
	}

	.pager .pager__item.is-active {
		border: none
	}

	.pager .pager__item.is-active a {
		color: #71716f;
		font-weight: 700
	}

	.pager .pager__item.pager__item--next,
	.pager .pager__item.pager__item--previous {
		border: none
	}

	.pager .pager__item.pager__item--next a,
	.pager .pager__item.pager__item--previous a {
		display: inline-block;
		width: 2.9rem;
		height: 2.9rem;
		margin-top: -.4rem;
		border: none;
		font-size: 3rem
	}

	.pager .pager__item.pager__item--next a:hover svg,
	.pager .pager__item.pager__item--previous a:hover svg {
		fill: #93bf37
	}

	.pager .pager__item.pager__item--next svg,
	.pager .pager__item.pager__item--previous svg {
		width: 100%;
		height: 100%;
		fill: #5a7826;
		-webkit-transition: fill .3s ease;
		transition: fill .3s ease
	}

	.pager .pager__item.pager__item--next {
		margin-right: 0;
		padding-right: 0
	}

	.pager .pager__item.pager__item--previous {
		margin-left: 0;
		padding-left: 0
	}

	.pager .pager__item.pager__item--previous svg {
		-webkit-transform: rotate(180deg);
		transform: rotate(180deg)
	}

	.pager .pager__item.pager__item--ellipsis:hover {
		border-color: #5a7826
	}

	@media screen and (min-width:46.875em) {
		.pager {
			margin-top: 6rem
		}
	}

	@media screen and (min-width:64em) {
		.pager {
			margin-top: 8rem
		}
	}

	.page-search-node .messages--error {
		display: none
	}

	.page-search-node .content-area {
		padding-bottom: 4rem
	}

	@media screen and (min-width:64em) {
		.page-search-node .content-area {
			padding-bottom: 8rem
		}
	}

	.page-search-node .search-form {
		max-width: 64rem;
		margin-bottom: 6rem;
		padding-top: 4rem
	}

	.page-search-node .search-form input:not([type=submit]) {
		width: 100%;
		height: 5rem;
		font-size: 1.6rem
	}

	.page-search-node .search-form .form-submit {
		margin-top: 2rem
	}

	@media screen and (min-width:64em) {
		.page-search-node .search-form .form-submit {
			height: 5rem;
			margin-top: 2.9rem
		}
	}

	.page-search-node .search-form .search-help-link {
		display: none
	}

	@media screen and (min-width:64em) {
		.page-search-node .search-form {
			margin-bottom: 8rem
		}

		.page-search-node .search-form .form-wrapper {
			display: -webkit-box;
			display: -ms-flexbox;
			display: flex;
			height: 8rem
		}

		.page-search-node .search-form .form-item {
			-webkit-box-flex: 1;
			-ms-flex-positive: 1;
			flex-grow: 1;
			margin-right: -.2rem
		}
	}

	.page-search-node h2 {
		position: relative;
		font-size: 3.4rem;
		line-height: 1.24;
		letter-spacing: -.02rem
	}

	.page-search-node h2:before {
		top: .8rem
	}

	.page-search-node .search-advanced.form-wrapper {
		display: none
	}

	.page-search-node .search-result {
		max-width: 64rem;
		margin-bottom: 4rem
	}

	.page-search-node .search-result li {
		margin-bottom: 4rem
	}

	.page-search-node .pager {
		margin-right: 0;
		margin-left: 0
	}

	@media screen and (min-width:64em) {
		.page-404 {
			padding-bottom: 4rem
		}
	}

	.svg-angle-right,
	.svg-bg-angle,
	.svg-exclamation-triangle,
	.svg-facebook,
	.svg-instagram,
	.svg-linkedin,
	.svg-right-angle-green,
	.svg-search,
	.svg-share-alt,
	.svg-twitter,
	.svg-youtube {
		background: url(/themes/cam/css/svg/sprite.view.svg) no-repeat
	}

	.svg-angle-right {
		background-position: 0 0
	}

	.svg-angle-right-dims {
		width: 16px;
		height: 32px
	}

	.svg-bg-angle {
		background-position: 63.05004597399186% 0
	}

	.svg-bg-angle-dims {
		width: 21.87px;
		height: 32px
	}

	.svg-exclamation-triangle {
		background-position: 24.242424242424242% 0
	}

	.svg-exclamation-triangle-dims {
		width: 32px;
		height: 28.44px
	}

	.svg-facebook {
		background-position: 94.41891891891892% 50%
	}

	.svg-facebook-dims {
		width: 24px;
		height: 24px
	}

	.svg-instagram {
		background-position: 0 100%
	}

	.svg-instagram-dims {
		width: 24px;
		height: 24px
	}

	.svg-linkedin {
		background-position: 97.29729729729729% 100%
	}

	.svg-linkedin-dims {
		width: 24px;
		height: 24px
	}

	.svg-right-angle-green {
		background-position: 0 57.142857142857146%
	}

	.svg-right-angle-green-dims {
		width: 22.15px;
		height: 32px
	}

	.svg-search {
		background-position: 33.56060606060606% 57.142857142857146%
	}

	.svg-search-dims {
		width: 32px;
		height: 32px
	}

	.svg-share-alt {
		background-position: 99.81428571428572% 0
	}

	.svg-share-alt-dims {
		width: 28px;
		height: 32px
	}

	.svg-twitter {
		background-position: 64.86486486486487% 100%
	}

	.svg-twitter-dims {
		width: 24px;
		height: 24px
	}

	.svg-youtube {
		background-position: 32.432432432432435% 100%
	}

	.svg-youtube-dims {
		width: 24px;
		height: 24px
	}

	html {
		box-sizing: border-box;
	}

	*,
	*::after,
	*::before {
		box-sizing: inherit;
	}

	ul.tabs {
		width: 100%;
		margin: 0 0 5px;
		border-bottom: 1px solid #ddd;
	}

	ul.tabs li {
		display: inline-block;
	}

	ul.tabs a {
		display: block;
		border: 1px solid #ddd;
		border-bottom: 0;
		border-radius: 3px 3px 0 0;
	}