registry := "gitlab-registry.camosun.ca/docker/cdn"
version := "testing"

_default:
  @just --list

build version:
  docker build -t {{ registry }} --no-cache .
  docker image tag {{ registry }}:latest {{ registry }}:{{ version }}

build_version: (build version)

push:
  docker push --all-tags {{ registry }}

tag version:
  git tag {{ version }} -m "Updated to v{{ version }}"

tag_version: (tag version)

tag_rm version:
  git tag -d {{ version }}
