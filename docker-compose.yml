version: "3.8"

services:
  app:
    image: gitlab-registry.camosun.ca/docker/${APP_NAME}:latest
    
    env_file:
      - stack.env

    networks:
      - traefik_${DEPLOYMENT}_network

    deploy:
      placement:
        constraints:
          - "node.labels.node_type == gluster"
      labels:
        # Traefik base config
        - traefik.enable=true
        - traefik.docker.network=traefik_${DEPLOYMENT}_network
        - traefik.constraint-label=traefik_${DEPLOYMENT}_service
        # https config
        - traefik.http.routers.${APP_NAME}-router-https.rule=Host(`${APP_NAME}.${DEPLOYMENT}.camosun.ca`)
        - traefik.http.routers.${APP_NAME}-router-https.entrypoints=https
        - traefik.http.routers.${APP_NAME}-router-https.tls=true
        - traefik.http.routers.${APP_NAME}-router-https.service=${APP_NAME}-service
        # service config
        - traefik.http.services.${APP_NAME}-service.loadbalancer.server.port=80
        - traefik.http.services.${APP_NAME}-service.loadbalancer.server.scheme=http

networks:
  traefik_dev_network:
    external: true
  traefik_sys_network:
    external: true
  traefik_app_network:
    external: true
