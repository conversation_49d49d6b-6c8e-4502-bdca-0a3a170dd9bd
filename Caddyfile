# The Caddyfile is an easy way to configure your Caddy web server.
#
# Unless the file starts with a global options block, the first
# uncommented line is always the address of your site.
#
# To use your own domain name (with automatic HTTPS), first make
# sure your domain's A/AAAA DNS records are properly pointed to
# this machine's public IP, then replace ":80" below with your
# domain name.
:80 {
	# Set this path to your site's directory.
	root * /srv

	# Enable gzip compression (equivalent to mod_deflate)
	encode gzip

	# Log all requests with IP and file served
	log {
		output file /var/log/caddy/access.log {
			roll_size 100mb
			roll_keep 5
			roll_keep_for 720h
		}
		format json {
			time_format "2006-01-02T15:04:05.000Z07:00"
			message_key "msg"
		}
		level INFO
	}

	# Enable the static file server (directory browsing disabled by default)
	file_server
}

# Refer to the Caddy docs for more information:
# https://caddyserver.com/docs/caddyfile


