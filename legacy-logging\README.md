## Scripts for Logging Legacy Requests

This directory contains scripts for logging requests to a legacy CDN. The main script is `log.php`, which logs requests to a file. The `.htaccess` file is used to rewrite all requests to `log.php` for logging.

The `log.php` script logs the following information for each request:
    - IP address
    - Date and time
    - Requested URI
    - Referrer
    - User agent

The log file is named `.requests.log` and is located in the same directory as the `log.php` script. The log file is a plain text file, with each log entry on a new line.

Permissions for the log file should be set to 776, to allow the web server to write to the file, but prevent others from reading it.

