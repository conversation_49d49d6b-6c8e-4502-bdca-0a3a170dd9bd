## Scripts for Logging Legacy Requests

This directory contains scripts for logging requests to a legacy CDN. The main script is `log.php`, which logs requests to a file. The `.htaccess` file is used to rewrite all requests to `log.php` for logging.

The `log.php` script logs the following information for each request:
    - IP address
    - Date and time
    - Requested URI
    - Referrer
    - User agent

The log file is named `.requests.log` and is located in the same directory as the `log.php` script. The log file is a plain text file, with each log entry on a new line.

Permissions for the log file should be set to 776, to allow the web server to write to the file, but prevent others from reading it.

We also had to update the VHOSTS for webservices since the files are not served on cdn.webservices.camosun.ca but actually through a subdirectory of https://webservices.camosun.ca/cdn

```<VirtualHost *:443>
    ServerName webservices.camosun.ca
    # ... other directives ...
    DocumentRoot /var/www/vhosts/webservices.camosun.ca/secure
    # ... other directives ...

    # This block explicitly configures the /cdn/ directory
    <Directory /var/www/vhosts/webservices.camosun.ca/secure/cdn>
        # Allow .htaccess to be used to override directives
        AllowOverride All

        # Explicitly set PHP handler for all files in this directory and its subdirectories
        <FilesMatch \.php$>
            SetHandler application/x-httpd-php
        </FilesMatch>

        # This is also important to ensure rewrite rules work
        Options Indexes FollowSymLinks
    </Directory>

</VirtualHost>
```