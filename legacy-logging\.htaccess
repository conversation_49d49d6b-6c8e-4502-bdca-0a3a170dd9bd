<IfModule mod_deflate.c>
	SetOutputFilter DEFLATE
	AddOutputFilterByType DEFLATE text/html text/css text/plain text/xml application/x-javascript application/x-httpd-php
    BrowserMatch ^Mozilla/4 gzip-only-text/html
    BrowserMatch ^Mozilla/4\.0[678] no-gzip
    BrowserMatch \bMSIE !no-gzip !gzip-only-text/html
   <IfModule mod_headers.c>
      # properly handle requests coming from behind proxies
      Header append Vary User-Agent
    </IfModule>
</IfModule>
Options -Indexes

<IfModule mod_deflate.c>
	RewriteEngine on
</IfModule>

# Set the path to your log file. The leading dot makes it a hidden file.
# Make sure the web server has write permissions to this file.
SetEnv LOG_FILE_PATH .requests.log

# Rewrite all requests to log.php for logging
RewriteRule . log.php [L]