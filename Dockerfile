# Multi-stage build to compile Caddy with Go 1.24.6
FROM golang:1.24.5-alpine3.22 AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates

# Set working directory
WORKDIR /build

# Download and build Caddy from source
RUN go install github.com/caddyserver/xcaddy/cmd/xcaddy@latest && \
    xcaddy build --output /build/caddy

# Final stage - use minimal alpine image
FROM alpine:3.22

# Install ca-certificates for HTTPS
RUN apk add --no-cache ca-certificates

# Create caddy user and group
RUN addgroup -g 1000 caddy && \
    adduser -D -s /bin/sh -u 1000 -G caddy caddy

# Create log directory and set permissions
RUN mkdir -p /var/log/caddy && \
    chown -R caddy:caddy /var/log/caddy && \
    chmod 755 /var/log/caddy

# Copy Caddy binary from builder stage
COPY --from=builder /build/caddy /usr/bin/caddy

# Set proper permissions for Caddy binary
RUN chmod +x /usr/bin/caddy

# Create necessary directories
RUN mkdir -p /etc/caddy /srv && \
    chown -R caddy:caddy /etc/caddy /srv

# Copy configuration and content
COPY Caddyfile /etc/caddy/Caddyfile
COPY html /srv

# Set ownership for copied files
RUN chown -R caddy:caddy /etc/caddy /srv

# Switch to caddy user
USER caddy

# Expose port 80
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD caddy version || exit 1

# Run Caddy
CMD ["caddy", "run", "--config", "/etc/caddy/Caddyfile", "--adapter", "caddyfile"]
